#!/usr/bin/env python3
"""
Unsloth MAAS Platform - High-Speed Inference with Unsloth
Ultra-fast inference platform using Unsloth optimization.
Optimized for performance, concurrency, and resource management.
"""

import asyncio
import logging
import time
import psutil
import os
import json
import shutil
import threading
import gc
from typing import Dict, List, Optional, Tuple, Any
from contextlib import asynccontextmanager
from concurrent.futures import ThreadPoolExecutor
import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel, Field

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Try to import GPU monitoring
try:
    import pynvml
    NVIDIA_ML_AVAILABLE = True
except ImportError:
    NVIDIA_ML_AVAILABLE = False
    logger.warning("pynvml not available, GPU monitoring disabled")

# Try to import Unsloth
try:
    import unsloth
    from unsloth import FastLanguageModel
    import torch
    UNSLOTH_AVAILABLE = True
    logger.info(f"✅ Unsloth available - version {unsloth.__version__}")
    logger.info(f"✅ PyTorch version: {torch.__version__}")
    logger.info(f"✅ CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        logger.info(f"✅ GPU count: {torch.cuda.device_count()}")
except ImportError as e:
    UNSLOTH_AVAILABLE = False
    logger.warning(f"⚠️ Unsloth not available: {e}")
    logger.warning("Using mock mode for testing")
except Exception as e:
    UNSLOTH_AVAILABLE = False
    logger.error(f"❌ Unsloth import error: {e}")
    logger.warning("Using mock mode due to import error")

# Application lifecycle management
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application startup and shutdown."""
    # Startup
    logger.info("🚀 Starting Unsloth MAAS Platform...")

    # Initialize GPU monitoring
    gpu_initialized = gpu_monitor.initialize()
    if gpu_initialized:
        logger.info(f"✅ GPU monitoring initialized for {gpu_monitor.get_gpu_count()} GPUs")
    else:
        logger.warning("⚠️ GPU monitoring not available")

    # Log system info
    logger.info(f"🐍 Python version: {os.sys.version_info[:2]}")
    logger.info(f"🦥 Unsloth available: {UNSLOTH_AVAILABLE}")
    if UNSLOTH_AVAILABLE:
        try:
            import torch
            logger.info(f"🔥 PyTorch version: {torch.__version__}")
            logger.info(f"⚡ CUDA available: {torch.cuda.is_available()}")
        except ImportError:
            pass

    yield

    # Shutdown
    logger.info("🛑 Shutting down Unsloth MAAS Platform...")

    # Cleanup models
    model_manager.cleanup()
    logger.info("✅ Model cleanup completed")

    # Final GPU cleanup
    if UNSLOTH_AVAILABLE:
        try:
            import torch
            torch.cuda.empty_cache()
            gc.collect()
        except ImportError:
            pass

    logger.info("👋 Shutdown completed")

# Create FastAPI app with lifecycle management
app = FastAPI(
    title="Unsloth MAAS Platform",
    description="High-Speed Model as a Service platform powered by Unsloth - Optimized Edition",
    version="2.1.0-optimized",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Thread-safe global state management
class ModelManager:
    """Thread-safe model management with optimized resource handling."""

    def __init__(self):
        self._lock = threading.RLock()
        self._loaded_models: Dict[str, dict] = {}
        self._unsloth_models: Dict[str, dict] = {}
        self._model_stats: Dict[str, dict] = {}
        self._gpu_count = 0
        self._executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="unsloth")

    def get_loaded_models(self) -> Dict[str, dict]:
        with self._lock:
            return self._loaded_models.copy()

    def get_model(self, model_name: str) -> Optional[dict]:
        with self._lock:
            return self._unsloth_models.get(model_name)

    def add_model(self, model_name: str, model_data: dict, model_info: dict):
        with self._lock:
            self._unsloth_models[model_name] = model_data
            self._loaded_models[model_name] = model_info
            self._model_stats[model_name] = {
                "requests": 0,
                "total_latency": 0.0,
                "avg_latency": 0.0,
                "last_used": time.time()
            }

    def remove_model(self, model_name: str) -> bool:
        with self._lock:
            removed = False
            if model_name in self._unsloth_models:
                # Cleanup GPU memory
                if UNSLOTH_AVAILABLE:
                    try:
                        import torch
                        del self._unsloth_models[model_name]
                        torch.cuda.empty_cache()
                        gc.collect()
                    except Exception as e:
                        logger.warning(f"Error during model cleanup: {e}")
                else:
                    del self._unsloth_models[model_name]
                removed = True

            self._loaded_models.pop(model_name, None)
            self._model_stats.pop(model_name, None)
            return removed

    def update_stats(self, model_name: str, latency: float):
        with self._lock:
            if model_name in self._model_stats:
                stats = self._model_stats[model_name]
                stats["requests"] += 1
                stats["total_latency"] += latency
                stats["avg_latency"] = stats["total_latency"] / stats["requests"]
                stats["last_used"] = time.time()

                # Update loaded_models for backward compatibility
                if model_name in self._loaded_models:
                    self._loaded_models[model_name]["requests"] = stats["requests"]
                    self._loaded_models[model_name]["avg_latency"] = stats["avg_latency"]

    def set_gpu_count(self, count: int):
        with self._lock:
            self._gpu_count = count

    def get_gpu_count(self) -> int:
        with self._lock:
            return self._gpu_count

    def cleanup(self):
        """Cleanup all resources."""
        with self._lock:
            for model_name in list(self._unsloth_models.keys()):
                self.remove_model(model_name)
            self._executor.shutdown(wait=True)

# Global model manager instance
model_manager = ModelManager()

# Enhanced Pydantic models with validation
class ModelLoadRequest(BaseModel):
    model_name: str = Field(..., min_length=1, description="Name of the model to load")
    model_path: Optional[str] = Field(None, description="Custom path to model files")
    max_seq_length: int = Field(2048, ge=128, le=32768, description="Maximum sequence length")
    dtype: Optional[str] = Field(None, pattern="^(float16|bfloat16|float32)?$", description="Model data type")
    load_in_4bit: bool = Field(True, description="Enable 4-bit quantization for memory efficiency")
    gpu_device: Optional[int] = Field(None, ge=0, description="Specific GPU device ID")
    device_map: Optional[str] = Field(None, pattern="^(auto|balanced|sequential)?$", description="Multi-GPU device mapping")
    gpu_memory_utilization: float = Field(0.8, ge=0.1, le=0.95, description="GPU memory utilization ratio")
    trust_remote_code: bool = Field(False, description="Trust remote code execution")

class QuickLoadRequest(BaseModel):
    model_name: str = Field(..., min_length=1, description="Model directory name in ./models/")
    max_seq_length: int = Field(2048, ge=128, le=32768, description="Maximum sequence length")
    load_in_4bit: bool = Field(True, description="Enable 4-bit quantization")
    gpu_device: Optional[int] = Field(None, ge=0, description="Specific GPU device ID")
    device_map: Optional[str] = Field(None, pattern="^(auto|balanced|sequential)?$", description="Multi-GPU mapping")
    use_multi_gpu: bool = Field(False, description="Enable multi-GPU mode")

class GenerateRequest(BaseModel):
    model_name: str = Field(..., min_length=1, description="Name of the loaded model")
    prompt: str = Field(..., min_length=1, max_length=8192, description="Input prompt for generation")
    max_new_tokens: int = Field(512, ge=1, le=4096, description="Maximum new tokens to generate")
    temperature: float = Field(0.7, ge=0.0, le=2.0, description="Sampling temperature")
    top_p: float = Field(0.9, ge=0.0, le=1.0, description="Top-p (nucleus) sampling")
    top_k: int = Field(-1, ge=-1, description="Top-k sampling (-1 for disabled)")
    do_sample: bool = Field(True, description="Enable sampling")
    use_cache: bool = Field(True, description="Use KV cache for efficiency")
    repetition_penalty: float = Field(1.0, ge=0.0, le=2.0, description="Repetition penalty")

class BatchGenerateRequest(BaseModel):
    model_name: str = Field(..., min_length=1, description="Name of the loaded model")
    prompts: List[str] = Field(..., min_items=1, max_items=16, description="List of prompts for batch generation")
    max_new_tokens: int = Field(512, ge=1, le=4096, description="Maximum new tokens per prompt")
    temperature: float = Field(0.7, ge=0.0, le=2.0, description="Sampling temperature")
    top_p: float = Field(0.9, ge=0.0, le=1.0, description="Top-p sampling")
    do_sample: bool = Field(True, description="Enable sampling")

class GenerateResponse(BaseModel):
    generated_text: str
    tokens_generated: int
    latency: float
    model_name: str
    engine: str = "unsloth"
    prompt_tokens: int = 0
    total_tokens: int = 0

class BatchGenerateResponse(BaseModel):
    results: List[GenerateResponse]
    total_latency: float
    batch_size: int
    model_name: str
    engine: str = "unsloth"

class ModelInfo(BaseModel):
    name: str
    status: str
    model_path: str
    max_seq_length: int = 2048
    load_in_4bit: bool = True
    total_requests: int = 0
    avg_latency: float = 0.0
    gpu_device: Optional[int] = None
    device_map: Optional[str] = None
    memory_usage_mb: float = 0.0
    last_used: Optional[float] = None

class GPUMonitor:
    """Optimized GPU monitoring with caching and error handling."""

    def __init__(self):
        self._initialized = False
        self._gpu_count = 0
        self._last_metrics = None
        self._last_update = 0
        self._cache_duration = 1.0  # Cache metrics for 1 second
        self._lock = threading.Lock()

    def initialize(self) -> bool:
        """Initialize GPU monitoring with proper error handling."""
        with self._lock:
            if self._initialized:
                return self._gpu_count > 0

            if not NVIDIA_ML_AVAILABLE:
                logger.warning("NVIDIA ML not available, GPU monitoring disabled")
                return False

            try:
                pynvml.nvmlInit()
                self._gpu_count = pynvml.nvmlDeviceGetCount()
                self._initialized = True
                model_manager.set_gpu_count(self._gpu_count)
                logger.info(f"✅ Initialized GPU monitoring for {self._gpu_count} GPUs")
                return True
            except Exception as e:
                logger.error(f"❌ Failed to initialize NVIDIA ML: {e}")
                self._gpu_count = 0
                return False

    def get_gpu_count(self) -> int:
        """Get number of available GPUs."""
        return self._gpu_count

    def get_metrics(self, force_refresh: bool = False) -> Optional[dict]:
        """Get GPU metrics with caching for performance."""
        current_time = time.time()

        with self._lock:
            # Return cached metrics if recent enough
            if (not force_refresh and
                self._last_metrics and
                current_time - self._last_update < self._cache_duration):
                return self._last_metrics

            if not self._initialized or self._gpu_count == 0:
                return None

            try:
                gpu_metrics = []
                for i in range(self._gpu_count):
                    try:
                        handle = pynvml.nvmlDeviceGetHandleByIndex(i)

                        # Get basic info
                        name = self._get_gpu_name(handle, i)
                        mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)

                        # Get utilization (with fallback)
                        gpu_util = self._get_gpu_utilization(handle)

                        # Get temperature (with fallback)
                        temp = self._get_gpu_temperature(handle)

                        gpu_metrics.append({
                            "gpu_id": i,
                            "name": name,
                            "gpu_utilization": gpu_util,
                            "memory_total": mem_info.total,
                            "memory_used": mem_info.used,
                            "memory_free": mem_info.free,
                            "memory_percent": (mem_info.used / mem_info.total) * 100,
                            "temperature": temp,
                            "available": mem_info.free > (2 * 1024 * 1024 * 1024)  # At least 2GB free
                        })

                    except Exception as e:
                        logger.warning(f"Error getting info for GPU {i}: {e}")
                        gpu_metrics.append(self._get_fallback_gpu_info(i))

                self._last_metrics = {
                    "gpus": gpu_metrics,
                    "timestamp": current_time
                }
                self._last_update = current_time

                return self._last_metrics

            except Exception as e:
                logger.error(f"Error collecting GPU metrics: {e}")
                return None

    def _get_gpu_name(self, handle, gpu_id: int) -> str:
        """Get GPU name with fallback."""
        try:
            name = pynvml.nvmlDeviceGetName(handle)
            if isinstance(name, bytes):
                name = name.decode('utf-8')
            return name
        except Exception:
            return f"GPU {gpu_id}"

    def _get_gpu_utilization(self, handle) -> int:
        """Get GPU utilization with fallback."""
        try:
            util = pynvml.nvmlDeviceGetUtilizationRates(handle)
            return util.gpu
        except Exception:
            return 0

    def _get_gpu_temperature(self, handle) -> int:
        """Get GPU temperature with fallback."""
        try:
            return pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
        except Exception:
            return 0

    def _get_fallback_gpu_info(self, gpu_id: int) -> dict:
        """Get fallback GPU info when monitoring fails."""
        return {
            "gpu_id": gpu_id,
            "name": f"GPU {gpu_id}",
            "gpu_utilization": 0,
            "memory_total": 0,
            "memory_used": 0,
            "memory_free": 0,
            "memory_percent": 0,
            "temperature": 0,
            "available": False
        }

# Global GPU monitor instance
gpu_monitor = GPUMonitor()

def get_gpu_metrics():
    """Get current GPU metrics."""
    if not NVIDIA_ML_AVAILABLE or gpu_count == 0:
        return None
    
    try:
        gpu_metrics = []
        for i in range(gpu_count):
            try:
                handle = pynvml.nvmlDeviceGetHandleByIndex(i)
                
                # GPU name
                try:
                    name = pynvml.nvmlDeviceGetName(handle)
                    if isinstance(name, bytes):
                        name = name.decode('utf-8')
                except Exception:
                    name = f"GPU {i}"
                
                # Memory info
                mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                
                # Utilization
                try:
                    util = pynvml.nvmlDeviceGetUtilizationRates(handle)
                    gpu_util = util.gpu
                except Exception:
                    gpu_util = 0
                
                # Temperature
                try:
                    temp = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
                except Exception:
                    temp = 0
                
                gpu_metrics.append({
                    "gpu_id": i,
                    "name": name,
                    "gpu_utilization": gpu_util,
                    "memory_total": mem_info.total,
                    "memory_used": mem_info.used,
                    "memory_free": mem_info.free,
                    "memory_percent": (mem_info.used / mem_info.total) * 100,
                    "temperature": temp,
                    "available": mem_info.free > (2 * 1024 * 1024 * 1024)  # At least 2GB free
                })
                
            except Exception as e:
                logger.warning(f"Error getting info for GPU {i}: {e}")
                gpu_metrics.append({
                    "gpu_id": i,
                    "name": f"GPU {i}",
                    "gpu_utilization": 0,
                    "memory_total": 0,
                    "memory_used": 0,
                    "memory_free": 0,
                    "memory_percent": 0,
                    "temperature": 0,
                    "available": False
                })
        
        return {
            "gpus": gpu_metrics,
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error(f"Error collecting GPU metrics: {e}")
        return None

def get_system_metrics():
    """Get current system metrics."""
    try:
        cpu_percent = psutil.cpu_percent(interval=None)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            "cpu_percent": cpu_percent,
            "cpu_count": psutil.cpu_count(),
            "memory_total": memory.total,
            "memory_used": memory.used,
            "memory_percent": memory.percent,
            "disk_total": disk.total,
            "disk_used": disk.used,
            "disk_percent": (disk.used / disk.total) * 100,
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error(f"Error collecting system metrics: {e}")
        return {
            "cpu_percent": 0,
            "memory_percent": 0,
            "disk_percent": 0,
            "timestamp": time.time()
        }

def scan_available_models(models_dir: str = "./models"):
    """Scan for available models in the models directory."""
    available_models = []
    
    if not os.path.exists(models_dir):
        logger.warning(f"Models directory {models_dir} does not exist")
        return available_models
    
    try:
        for item in os.listdir(models_dir):
            model_path = os.path.join(models_dir, item)
            
            if not os.path.isdir(model_path):
                continue
            
            # Check for model files
            model_files = []
            config_files = []
            
            for file in os.listdir(model_path):
                file_lower = file.lower()
                if any(ext in file_lower for ext in ['.bin', '.safetensors', '.pt', '.pth']):
                    model_files.append(file)
                elif file_lower in ['config.json', 'model.json', 'tokenizer.json']:
                    config_files.append(file)
            
            # Calculate directory size
            total_size = 0
            file_count = 0
            try:
                for root, dirs, files in os.walk(model_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        if os.path.isfile(file_path):
                            total_size += os.path.getsize(file_path)
                            file_count += 1
            except:
                total_size = 0
                file_count = 0
            
            # Determine if this looks like a valid model directory
            is_valid_model = (
                len(model_files) > 0 or
                'config.json' in config_files or
                len(config_files) > 0
            )
            
            if is_valid_model:
                model_info = {
                    "name": item,
                    "path": model_path,
                    "size_bytes": total_size,
                    "size_human": human_readable_size(total_size),
                    "file_count": file_count,
                    "model_files": len(model_files),
                    "config_files": config_files,
                    "model_type": "unknown",
                    "parameters": "unknown"
                }
                
                # Try to read config.json for more details
                config_path = os.path.join(model_path, 'config.json')
                if os.path.exists(config_path):
                    try:
                        with open(config_path, 'r') as f:
                            config = json.load(f)
                            model_info["model_type"] = config.get("model_type", "unknown")
                            model_info["architectures"] = config.get("architectures", [])
                            
                            # Try to estimate parameters
                            hidden_size = config.get("hidden_size", 0)
                            num_layers = config.get("num_hidden_layers", 0)
                            if hidden_size and num_layers:
                                estimated_params = (hidden_size * hidden_size * num_layers * 12) // 1000000
                                if estimated_params > 1000:
                                    model_info["parameters"] = f"~{estimated_params//1000}B"
                                else:
                                    model_info["parameters"] = f"~{estimated_params}M"
                    except Exception as e:
                        logger.debug(f"Could not read config for {item}: {e}")
                
                available_models.append(model_info)
        
        available_models.sort(key=lambda x: x["name"])
        logger.info(f"Found {len(available_models)} available models in {models_dir}")
        
    except Exception as e:
        logger.error(f"Error scanning models directory: {e}")
    
    return available_models

def human_readable_size(size_bytes):
    """Convert bytes to human readable format."""
    if size_bytes == 0:
        return "0 B"
    
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} PB"

@app.get("/")
async def root():
    """Serve the main web interface."""
    try:
        with open("web/index_unsloth.html", "r") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        try:
            with open("web/index_vllm.html", "r") as f:
                return HTMLResponse(content=f.read())
        except FileNotFoundError:
            return HTMLResponse(content="""
            <html>
                <body>
                    <h1>🦥 Unsloth MAAS Platform - Optimized</h1>
                    <p>High-Speed Model as a Service platform powered by Unsloth</p>
                    <p><strong>New Features:</strong> Async inference, batch processing, optimized memory management</p>
                    <p>Visit <a href="/docs">/docs</a> for API documentation.</p>
                </body>
            </html>
            """)

@app.get("/health")
async def health_check():
    """Enhanced health check with detailed system status."""
    loaded_models = model_manager.get_loaded_models()
    gpu_metrics = gpu_monitor.get_metrics()

    health_data = {
        "status": "healthy",
        "engine": "unsloth-optimized",
        "version": "2.1.0",
        "unsloth_available": UNSLOTH_AVAILABLE,
        "models_loaded": len(loaded_models),
        "gpu_count": gpu_monitor.get_gpu_count(),
        "features": {
            "async_inference": True,
            "batch_processing": True,
            "memory_optimization": True,
            "prompt_caching": True,
            "concurrent_requests": True
        }
    }

    if gpu_metrics:
        health_data["gpu_status"] = {
            "available_gpus": len([gpu for gpu in gpu_metrics["gpus"] if gpu["available"]]),
            "total_memory_gb": sum(gpu["memory_total"] for gpu in gpu_metrics["gpus"]) / (1024**3),
            "used_memory_gb": sum(gpu["memory_used"] for gpu in gpu_metrics["gpus"]) / (1024**3)
        }

    return health_data

@app.get("/models", response_model=List[str])
async def list_models():
    """List all loaded models."""
    return list(model_manager.get_loaded_models().keys())

@app.get("/models/info", response_model=List[ModelInfo])
async def get_models_info():
    """Get detailed information about all loaded models with enhanced metrics."""
    loaded_models = model_manager.get_loaded_models()
    models_info = []

    for name, info in loaded_models.items():
        models_info.append(ModelInfo(
            name=name,
            status=info["status"],
            model_path=info["model_path"],
            max_seq_length=info.get("max_seq_length", 2048),
            load_in_4bit=info.get("load_in_4bit", True),
            total_requests=info.get("requests", 0),
            avg_latency=info.get("avg_latency", 0.0),
            gpu_device=info.get("gpu_device"),
            device_map=info.get("device_map"),
            memory_usage_mb=info.get("memory_usage_mb", 0.0),
            last_used=info.get("last_used")
        ))
    return models_info

@app.get("/models/available")
async def get_available_models():
    """Get list of available models in the models directory."""
    try:
        available_models = scan_available_models()
        return {
            "available_models": available_models,
            "count": len(available_models),
            "models_directory": "./models"
        }
    except Exception as e:
        logger.error(f"Error getting available models: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to scan models: {str(e)}")

@app.get("/gpu/info")
async def get_gpu_info():
    """Get comprehensive GPU information with real-time metrics."""
    try:
        gpu_metrics = gpu_monitor.get_metrics(force_refresh=True)
        gpu_count = gpu_monitor.get_gpu_count()

        response = {
            "gpus": gpu_metrics["gpus"] if gpu_metrics else [],
            "count": gpu_count,
            "nvidia_ml_available": NVIDIA_ML_AVAILABLE,
            "monitoring_active": gpu_monitor._initialized,
            "cache_duration": gpu_monitor._cache_duration,
            "unsloth_optimizations": {
                "4bit_quantization": "Reduces memory usage by 75%",
                "flash_attention": "2x faster attention computation",
                "optimized_kernels": "Custom CUDA kernels for speed",
                "memory_efficient": "Gradient checkpointing enabled",
                "mixed_precision": "Automatic mixed precision for speed",
                "prompt_caching": "Cache frequent prompts for faster response"
            },
            "performance_features": {
                "async_inference": "Non-blocking inference processing",
                "batch_processing": "Process multiple prompts simultaneously",
                "memory_optimization": "Automatic GPU memory cleanup",
                "concurrent_requests": "Handle multiple requests in parallel",
                "smart_caching": "Intelligent prompt and result caching"
            },
            "multi_gpu_configs": [
                {
                    "name": "Auto Multi-GPU",
                    "device_map": "auto",
                    "description": "Automatically distribute model across all GPUs",
                    "recommended": True,
                    "memory_efficiency": "Optimal",
                    "min_gpus": 2
                },
                {
                    "name": "Balanced Multi-GPU",
                    "device_map": "balanced",
                    "description": "Evenly balance model layers across GPUs",
                    "recommended": True,
                    "memory_efficiency": "High",
                    "min_gpus": 2
                },
                {
                    "name": "Sequential Multi-GPU",
                    "device_map": "sequential",
                    "description": "Load model layers sequentially across GPUs",
                    "recommended": False,
                    "memory_efficiency": "Medium",
                    "min_gpus": 2
                }
            ]
        }

        # Add GPU utilization summary
        if gpu_metrics:
            total_memory = sum(gpu["memory_total"] for gpu in gpu_metrics["gpus"])
            used_memory = sum(gpu["memory_used"] for gpu in gpu_metrics["gpus"])
            avg_utilization = sum(gpu["gpu_utilization"] for gpu in gpu_metrics["gpus"]) / len(gpu_metrics["gpus"])

            response["summary"] = {
                "total_memory_gb": total_memory / (1024**3),
                "used_memory_gb": used_memory / (1024**3),
                "free_memory_gb": (total_memory - used_memory) / (1024**3),
                "memory_utilization_percent": (used_memory / total_memory) * 100,
                "average_gpu_utilization": avg_utilization,
                "available_gpus": len([gpu for gpu in gpu_metrics["gpus"] if gpu["available"]])
            }

        return response

    except Exception as e:
        logger.error(f"Error getting GPU info: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get GPU info: {str(e)}")

class ModelLoader:
    """Optimized model loading with validation and error handling."""

    def __init__(self):
        self._loading_lock = threading.Lock()
        self._loading_status = {}  # Track loading progress

    async def load_model(self, request: ModelLoadRequest) -> dict:
        """Load a model with comprehensive validation and optimization."""
        # Check if model is already loaded
        if model_manager.get_model(request.model_name):
            raise HTTPException(
                status_code=400,
                detail=f"Model {request.model_name} is already loaded"
            )

        # Check if model is currently being loaded
        with self._loading_lock:
            if request.model_name in self._loading_status:
                raise HTTPException(
                    status_code=409,
                    detail=f"Model {request.model_name} is currently being loaded"
                )
            self._loading_status[request.model_name] = "loading"

        try:
            # Validate and prepare model path
            model_path = self._validate_model_path(request)

            # Mock mode handling
            if not UNSLOTH_AVAILABLE:
                return self._load_mock_model(request, model_path)

            # Validate GPU resources
            self._validate_gpu_resources(request)

            # Load model in executor to avoid blocking
            result = await asyncio.get_event_loop().run_in_executor(
                model_manager._executor,
                self._load_real_model,
                request,
                model_path
            )

            return result

        except Exception as e:
            logger.error(f"Failed to load model {request.model_name}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to load model: {str(e)}")
        finally:
            # Clean up loading status
            with self._loading_lock:
                self._loading_status.pop(request.model_name, None)

    def _validate_model_path(self, request: ModelLoadRequest) -> str:
        """Validate and determine model path."""
        if request.model_path:
            model_path = request.model_path
            if not os.path.exists(model_path):
                raise HTTPException(
                    status_code=404,
                    detail=f"Custom model path not found: {model_path}"
                )
        else:
            model_path = os.path.join("./models", request.model_name)
            if not os.path.exists(model_path):
                available_models = [m['name'] for m in scan_available_models()]
                raise HTTPException(
                    status_code=404,
                    detail=f"Model directory not found: {model_path}. Available models: {available_models}"
                )

        # Validate model directory structure
        required_files = ['config.json']
        missing_files = []
        for file in required_files:
            if not os.path.exists(os.path.join(model_path, file)):
                missing_files.append(file)

        if missing_files:
            logger.warning(f"Missing recommended files in {model_path}: {missing_files}")

        return model_path

    def _validate_gpu_resources(self, request: ModelLoadRequest):
        """Validate GPU resources and configuration."""
        gpu_count = gpu_monitor.get_gpu_count()

        if request.gpu_device is not None:
            if request.gpu_device >= gpu_count:
                raise HTTPException(
                    status_code=400,
                    detail=f"GPU device {request.gpu_device} not available. Available GPUs: 0-{gpu_count-1}"
                )

        if request.device_map and gpu_count < 2:
            logger.warning(f"Multi-GPU device_map specified but only {gpu_count} GPU(s) available")

        # Check GPU memory
        gpu_metrics = gpu_monitor.get_metrics()
        if gpu_metrics:
            available_gpus = [gpu for gpu in gpu_metrics["gpus"] if gpu["available"]]
            if not available_gpus:
                raise HTTPException(
                    status_code=503,
                    detail="No GPUs have sufficient memory (>2GB) available"
                )

    def _load_mock_model(self, request: ModelLoadRequest, model_path: str) -> dict:
        """Load model in mock mode for testing."""
        model_info = {
            "status": "loaded",
            "model_path": model_path,
            "max_seq_length": request.max_seq_length,
            "load_in_4bit": request.load_in_4bit,
            "gpu_device": request.gpu_device,
            "device_map": request.device_map,
            "memory_usage_mb": 1024.0,  # Mock memory usage
            "last_used": time.time()
        }

        model_data = {
            "model": None,  # Mock model
            "tokenizer": None  # Mock tokenizer
        }

        model_manager.add_model(request.model_name, model_data, model_info)

        logger.info(f"✅ Mock model {request.model_name} loaded successfully")

        return {
            "message": f"Mock Unsloth model {request.model_name} loaded successfully from {model_path}",
            "model_name": request.model_name,
            "model_path": model_path,
            "engine": "unsloth-mock",
            "optimizations": ["4bit_quantization", "flash_attention", "optimized_kernels"],
            "memory_usage_mb": 1024.0
        }

    def _load_real_model(self, request: ModelLoadRequest, model_path: str) -> dict:
        """Load real model with Unsloth optimizations."""
        logger.info(f"Loading model {request.model_name} from {model_path} with Unsloth")

        try:
            # Prepare loading arguments
            load_kwargs = {
                "model_name": model_path,
                "max_seq_length": request.max_seq_length,
                "dtype": request.dtype,
                "load_in_4bit": request.load_in_4bit,
                "trust_remote_code": request.trust_remote_code,
            }

            # Configure GPU setup
            if request.device_map:
                load_kwargs["device_map"] = request.device_map
                logger.info(f"Using multi-GPU with device_map: {request.device_map}")
            elif request.gpu_device is not None:
                import torch
                torch.cuda.set_device(request.gpu_device)
                logger.info(f"Using single GPU device: {request.gpu_device}")
            else:
                logger.info("Using default GPU configuration")

            # Load model with Unsloth optimization
            model, tokenizer = FastLanguageModel.from_pretrained(**load_kwargs)

            # Enable fast inference mode
            FastLanguageModel.for_inference(model)

            # Calculate memory usage
            memory_usage_mb = self._estimate_memory_usage(model)

            # Prepare model info
            model_info = {
                "status": "loaded",
                "model_path": model_path,
                "max_seq_length": request.max_seq_length,
                "load_in_4bit": request.load_in_4bit,
                "gpu_device": request.gpu_device,
                "device_map": request.device_map,
                "memory_usage_mb": memory_usage_mb,
                "last_used": time.time()
            }

            model_data = {
                "model": model,
                "tokenizer": tokenizer
            }

            # Store in model manager
            model_manager.add_model(request.model_name, model_data, model_info)

            logger.info(f"✅ Model {request.model_name} loaded successfully with Unsloth optimizations")
            logger.info(f"📊 Estimated memory usage: {memory_usage_mb:.1f} MB")

            return {
                "message": f"Model {request.model_name} loaded successfully with Unsloth optimizations",
                "model_name": request.model_name,
                "model_path": model_path,
                "engine": "unsloth",
                "optimizations": ["4bit_quantization", "flash_attention", "optimized_kernels", "fast_inference"],
                "max_seq_length": request.max_seq_length,
                "load_in_4bit": request.load_in_4bit,
                "memory_usage_mb": memory_usage_mb
            }

        except Exception as e:
            # Clean up on failure
            if UNSLOTH_AVAILABLE:
                import torch
                torch.cuda.empty_cache()
                gc.collect()
            raise e

    def _estimate_memory_usage(self, model) -> float:
        """Estimate model memory usage in MB."""
        try:
            if UNSLOTH_AVAILABLE:
                import torch
                total_params = sum(p.numel() for p in model.parameters())
                # Rough estimation: 4 bytes per parameter for float32, 2 for float16, 0.5 for 4bit
                bytes_per_param = 0.5  # Assuming 4bit quantization
                memory_bytes = total_params * bytes_per_param
                return memory_bytes / (1024 * 1024)  # Convert to MB
            return 0.0
        except Exception:
            return 0.0

# Global model loader
model_loader = ModelLoader()

@app.post("/models/load")
async def load_model(request: ModelLoadRequest):
    """Load a model with optimized Unsloth configuration."""
    return await model_loader.load_model(request)

@app.post("/models/quick-load")
async def quick_load_model(request: QuickLoadRequest):
    """Quick load a model from ./models/ directory with Unsloth."""
    # Auto-configure multi-GPU if requested
    device_map = request.device_map
    if request.use_multi_gpu and not device_map:
        device_map = "auto"  # Default to auto for multi-GPU

    full_request = ModelLoadRequest(
        model_name=request.model_name,
        model_path=None,
        max_seq_length=request.max_seq_length,
        load_in_4bit=request.load_in_4bit,
        gpu_device=request.gpu_device,
        device_map=device_map,
        gpu_memory_utilization=0.8
    )

    return await load_model(full_request)

@app.post("/models/multi-gpu-load")
async def multi_gpu_load_model(request: QuickLoadRequest):
    """Load a model with optimized multi-GPU configuration."""
    if gpu_count < 2:
        raise HTTPException(
            status_code=400,
            detail=f"Multi-GPU loading requires at least 2 GPUs. Found: {gpu_count}"
        )

    # Force multi-GPU configuration
    full_request = ModelLoadRequest(
        model_name=request.model_name,
        model_path=None,
        max_seq_length=request.max_seq_length,
        load_in_4bit=request.load_in_4bit,
        device_map=request.device_map or "auto",  # Default to auto
        gpu_memory_utilization=0.8,
        gpu_device=None  # Multi-GPU mode, ignore single GPU setting
    )

    logger.info(f"Loading model {request.model_name} with multi-GPU configuration")
    result = await load_model(full_request)

    # Add multi-GPU specific information to response
    if isinstance(result, dict):
        result["multi_gpu"] = True
        result["gpu_count"] = gpu_count
        result["device_map"] = request.device_map or "auto"
        result["optimization_level"] = "multi-gpu-optimized"

    return result

@app.delete("/models/{model_name}")
async def unload_model(model_name: str, background_tasks: BackgroundTasks):
    """Unload a model with optimized cleanup."""
    if not model_manager.get_model(model_name):
        raise HTTPException(status_code=404, detail=f"Model {model_name} not found")

    try:
        # Get model info before removal
        loaded_models = model_manager.get_loaded_models()
        model_info = loaded_models.get(model_name, {})

        # Remove model (this handles GPU cleanup)
        success = model_manager.remove_model(model_name)

        if not success:
            raise HTTPException(status_code=500, detail=f"Failed to remove model {model_name}")

        # Schedule background cleanup
        background_tasks.add_task(cleanup_gpu_memory)

        logger.info(f"✅ Model {model_name} unloaded successfully")

        return {
            "message": f"Model {model_name} unloaded successfully",
            "model_name": model_name,
            "memory_freed_mb": model_info.get("memory_usage_mb", 0),
            "cleanup_scheduled": True
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to unload model {model_name}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to unload model: {str(e)}")

async def cleanup_gpu_memory():
    """Background task for GPU memory cleanup."""
    try:
        if UNSLOTH_AVAILABLE:
            import torch
            torch.cuda.empty_cache()
            gc.collect()
            logger.debug("GPU memory cleanup completed")
    except Exception as e:
        logger.warning(f"GPU cleanup warning: {e}")

@app.delete("/models/files/{model_name}")
async def delete_model_files(model_name: str):
    """Delete model files from disk."""
    try:
        # First check if model is currently loaded
        if model_name in loaded_models:
            raise HTTPException(
                status_code=400,
                detail=f"Cannot delete model '{model_name}' because it is currently loaded. Please unload it first."
            )

        # Check if model directory exists
        model_path = os.path.join("./models", model_name)
        if not os.path.exists(model_path):
            raise HTTPException(
                status_code=404,
                detail=f"Model directory not found: {model_path}"
            )

        if not os.path.isdir(model_path):
            raise HTTPException(
                status_code=400,
                detail=f"Path exists but is not a directory: {model_path}"
            )

        # Get model info before deletion
        available_models = scan_available_models()
        model_info = None
        for model in available_models:
            if model["name"] == model_name:
                model_info = model
                break

        # Delete the model directory
        logger.info(f"Deleting model directory: {model_path}")
        shutil.rmtree(model_path)

        logger.info(f"✅ Model '{model_name}' deleted successfully")

        return {
            "message": f"Model '{model_name}' deleted successfully",
            "model_name": model_name,
            "deleted_path": model_path,
            "deleted_size": model_info["size_human"] if model_info else "unknown",
            "deleted_files": model_info["file_count"] if model_info else 0
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete model {model_name}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete model: {str(e)}")

class InferenceEngine:
    """Optimized inference engine with async support and batching."""

    def __init__(self):
        self._generation_lock = threading.Lock()
        self._prompt_cache = {}  # Simple prompt caching
        self._cache_max_size = 100

    async def generate_single(self, request: GenerateRequest) -> GenerateResponse:
        """Generate text for a single prompt with optimizations."""
        model_data = model_manager.get_model(request.model_name)
        if not model_data:
            raise HTTPException(status_code=404, detail=f"Model {request.model_name} not loaded")

        start_time = time.time()

        if not UNSLOTH_AVAILABLE:
            return self._mock_generate(request, start_time)

        try:
            # Run inference in thread pool to avoid blocking
            result = await asyncio.get_event_loop().run_in_executor(
                model_manager._executor,
                self._run_inference,
                model_data,
                request,
                start_time
            )

            # Update statistics
            latency = time.time() - start_time
            model_manager.update_stats(request.model_name, latency)

            return result

        except Exception as e:
            logger.error(f"Inference failed for model {request.model_name}: {e}")
            raise HTTPException(status_code=500, detail=f"Text generation failed: {str(e)}")

    def _run_inference(self, model_data: dict, request: GenerateRequest, start_time: float) -> GenerateResponse:
        """Run actual inference with optimizations."""
        model = model_data["model"]
        tokenizer = model_data["tokenizer"]

        # Check prompt cache
        cache_key = f"{request.model_name}:{hash(request.prompt)}:{request.max_new_tokens}"
        if cache_key in self._prompt_cache and len(self._prompt_cache) < self._cache_max_size:
            cached_result = self._prompt_cache[cache_key]
            logger.debug(f"Using cached result for prompt hash: {hash(request.prompt)}")
            return GenerateResponse(
                generated_text=cached_result["text"],
                tokens_generated=cached_result["tokens"],
                latency=time.time() - start_time,
                model_name=request.model_name,
                engine="unsloth-cached"
            )

        # Tokenize input with optimizations
        with self._generation_lock:  # Prevent concurrent tokenization issues
            inputs = tokenizer(
                request.prompt,
                return_tensors="pt",
                truncation=True,
                max_length=model_manager.get_loaded_models()[request.model_name]["max_seq_length"],
                padding=False,  # No padding for single inference
                add_special_tokens=True
            ).to(model.device)

            prompt_tokens = inputs.input_ids.shape[1]

            # Prepare generation parameters
            generation_kwargs = {
                **inputs,
                "max_new_tokens": request.max_new_tokens,
                "temperature": request.temperature,
                "top_p": request.top_p,
                "do_sample": request.do_sample,
                "use_cache": request.use_cache,
                "pad_token_id": tokenizer.eos_token_id,
            }

            # Add optional parameters
            if request.top_k > 0:
                generation_kwargs["top_k"] = request.top_k
            if request.repetition_penalty != 1.0:
                generation_kwargs["repetition_penalty"] = request.repetition_penalty

            # Generate with memory optimization
            with torch.no_grad():
                if UNSLOTH_AVAILABLE:
                    import torch
                    with torch.cuda.amp.autocast(enabled=True):  # Use mixed precision
                        outputs = model.generate(**generation_kwargs)
                else:
                    outputs = model.generate(**generation_kwargs)

        # Decode generated text
        generated_tokens = outputs[0][prompt_tokens:]
        generated_text = tokenizer.decode(generated_tokens, skip_special_tokens=True)
        tokens_generated = len(generated_tokens)

        # Cache result if reasonable size
        if len(generated_text) < 2048 and len(self._prompt_cache) < self._cache_max_size:
            self._prompt_cache[cache_key] = {
                "text": generated_text,
                "tokens": tokens_generated
            }

        # Clean up GPU memory
        if UNSLOTH_AVAILABLE:
            import torch
            del inputs, outputs, generated_tokens
            torch.cuda.empty_cache()

        latency = time.time() - start_time

        logger.info(f"Generated {tokens_generated} tokens in {latency:.3f}s for model {request.model_name}")

        return GenerateResponse(
            generated_text=generated_text,
            tokens_generated=tokens_generated,
            latency=latency,
            model_name=request.model_name,
            engine="unsloth",
            prompt_tokens=prompt_tokens,
            total_tokens=prompt_tokens + tokens_generated
        )

    def _mock_generate(self, request: GenerateRequest, start_time: float) -> GenerateResponse:
        """Mock generation for testing."""
        mock_response = f"[Unsloth Mock] Ultra-fast generated response for prompt: '{request.prompt[:50]}...' using model {request.model_name}"
        model_manager.update_stats(request.model_name, 0.05)  # Mock fast latency

        return GenerateResponse(
            generated_text=mock_response,
            tokens_generated=len(mock_response.split()),
            latency=time.time() - start_time,
            model_name=request.model_name,
            engine="unsloth-mock"
        )

    async def generate_batch(self, request: BatchGenerateRequest) -> BatchGenerateResponse:
        """Generate text for multiple prompts in batch."""
        if not model_manager.get_model(request.model_name):
            raise HTTPException(status_code=404, detail=f"Model {request.model_name} not loaded")

        start_time = time.time()

        # Create individual requests
        individual_requests = [
            GenerateRequest(
                model_name=request.model_name,
                prompt=prompt,
                max_new_tokens=request.max_new_tokens,
                temperature=request.temperature,
                top_p=request.top_p,
                do_sample=request.do_sample
            )
            for prompt in request.prompts
        ]

        # Process in parallel
        tasks = [self.generate_single(req) for req in individual_requests]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Handle exceptions
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Batch generation failed for prompt {i}: {result}")
                # Create error response
                valid_results.append(GenerateResponse(
                    generated_text=f"[Error] Generation failed: {str(result)}",
                    tokens_generated=0,
                    latency=0.0,
                    model_name=request.model_name,
                    engine="unsloth-error"
                ))
            else:
                valid_results.append(result)

        total_latency = time.time() - start_time

        return BatchGenerateResponse(
            results=valid_results,
            total_latency=total_latency,
            batch_size=len(request.prompts),
            model_name=request.model_name,
            engine="unsloth"
        )

# Global inference engine
inference_engine = InferenceEngine()

@app.post("/generate", response_model=GenerateResponse)
async def generate_text(request: GenerateRequest):
    """Generate text using optimized Unsloth inference."""
    return await inference_engine.generate_single(request)

@app.post("/generate/batch", response_model=BatchGenerateResponse)
async def generate_batch(request: BatchGenerateRequest):
    """Generate text for multiple prompts in batch."""
    return await inference_engine.generate_batch(request)

@app.get("/metrics")
async def get_metrics():
    """Get comprehensive system, GPU, and model metrics."""
    try:
        # Get system metrics
        system_metrics = get_system_metrics()
        system_metrics["engine"] = "unsloth-optimized"
        system_metrics["version"] = "2.1.0"
        system_metrics["unsloth_available"] = UNSLOTH_AVAILABLE

        # Get GPU metrics
        gpu_metrics = gpu_monitor.get_metrics()

        # Get model metrics
        loaded_models = model_manager.get_loaded_models()
        model_metrics = {}

        for name, info in loaded_models.items():
            model_metrics[name] = {
                "requests": info.get("requests", 0),
                "avg_latency": info.get("avg_latency", 0.0),
                "status": info.get("status", "unknown"),
                "max_seq_length": info.get("max_seq_length", 2048),
                "load_in_4bit": info.get("load_in_4bit", True),
                "memory_usage_mb": info.get("memory_usage_mb", 0.0),
                "last_used": info.get("last_used"),
                "device_map": info.get("device_map"),
                "gpu_device": info.get("gpu_device")
            }

        # Calculate performance metrics
        total_requests = sum(info.get("requests", 0) for info in loaded_models.values())
        avg_latency_all = sum(info.get("avg_latency", 0) for info in loaded_models.values()) / max(len(loaded_models), 1)

        # Cache statistics
        cache_stats = {
            "prompt_cache_size": len(inference_engine._prompt_cache),
            "cache_max_size": inference_engine._cache_max_size,
            "cache_hit_rate": "N/A"  # Could be implemented with counters
        }

        response = {
            "system": system_metrics,
            "gpu": gpu_metrics,
            "models": model_metrics,
            "performance": {
                "total_models_loaded": len(loaded_models),
                "total_requests_served": total_requests,
                "average_latency_ms": avg_latency_all * 1000,
                "cache_statistics": cache_stats
            },
            "engine": "unsloth-optimized",
            "features": {
                "async_inference": "Active",
                "batch_processing": "Active",
                "memory_optimization": "Active",
                "prompt_caching": "Active",
                "concurrent_requests": "Active"
            },
            "optimizations": {
                "4bit_quantization": "Active",
                "flash_attention": "Active",
                "optimized_kernels": "Active",
                "memory_efficient": "Active",
                "mixed_precision": "Active",
                "smart_caching": "Active"
            },
            "timestamp": time.time()
        }

        return response

    except Exception as e:
        logger.error(f"Error getting metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {str(e)}")

if __name__ == "__main__":
    print("🦥 Starting Unsloth MAAS Platform - Optimized Edition...")
    print("=" * 60)
    print("🌐 Web interface: http://localhost:8000")
    print("📊 API docs: http://localhost:8000/docs")
    print("🔧 Engine: Unsloth (Ultra-Fast + Optimized)")
    print("✨ New Features:")
    print("   • Async inference for better concurrency")
    print("   • Batch processing for multiple prompts")
    print("   • Optimized memory management")
    print("   • Smart prompt caching")
    print("   • Enhanced error handling")
    print("   • Real-time GPU monitoring")
    print("=" * 60)
    print("Press Ctrl+C to stop")
    print()

    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=False,
            log_level="info",
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n👋 Gracefully shutting down...")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        logger.error(f"Server startup failed: {e}")
    finally:
        print("🛑 Server stopped")
