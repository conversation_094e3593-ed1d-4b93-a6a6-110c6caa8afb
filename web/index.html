<!DOCTYPE html>
<html>
<head>
    <title>vLLM MAAS Platform</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * { box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 { margin: 0; font-size: 2.5em; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
        }

        .section {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        input, select, textarea, button {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        button {
            background: #667eea;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
            transition: background 0.3s;
        }
        button:hover { background: #5a6fd8; }
        button:disabled { background: #ccc; cursor: not-allowed; }

        .model-item {
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
            background: #f9f9f9;
        }

        .model-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: bold;
        }

        .model-details {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-loaded { background: #28a745; }
        .status-loading { background: #ffc107; }
        .status-error { background: #dc3545; }

        .advanced-options {
            display: none;
            margin-top: 15px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f8f9fa;
        }

        .toggle-advanced {
            background: #6c757d !important;
            margin-bottom: 10px;
        }

        label {
            display: block;
            margin-top: 10px;
            font-weight: bold;
            color: #555;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .metric-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }

        .metric-label {
            color: #6c757d;
            font-size: 0.9em;
            margin-top: 5px;
        }

        #responseArea {
            min-height: 200px;
            font-family: monospace;
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            overflow-y: auto;
        }

        .local-model-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .local-model-card:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
        }

        .local-model-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .local-model-name {
            font-weight: bold;
            font-size: 1.1em;
            color: #333;
        }

        .local-model-type {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            text-transform: uppercase;
        }

        .local-model-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 8px;
            margin: 10px 0;
            font-size: 0.9em;
            color: #666;
        }

        .local-model-detail {
            display: flex;
            flex-direction: column;
        }

        .local-model-detail-label {
            font-weight: bold;
            color: #333;
        }

        .local-model-actions {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .load-model-btn {
            background: #28a745 !important;
            padding: 8px 16px !important;
            font-size: 0.9em !important;
            border-radius: 4px !important;
            border: none;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .load-model-btn:hover {
            background: #218838 !important;
        }

        .load-model-btn:disabled {
            background: #6c757d !important;
            cursor: not-allowed;
        }

        .progress-container {
            margin-top: 10px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.2);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
            position: relative;
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .progress-message {
            margin-top: 5px;
            font-size: 0.8em;
            color: #666;
            text-align: center;
        }

        .progress-stage {
            margin-top: 3px;
            font-size: 0.75em;
            color: #28a745;
            font-weight: bold;
            text-align: center;
        }

        .model-invalid {
            opacity: 0.6;
            border-color: #dc3545;
        }

        .model-invalid .local-model-type {
            background: #dc3545;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            .header h1 { font-size: 2em; }
            .local-model-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 vLLM MAAS Platform</h1>
            <p>High-Performance Model as a Service with vLLM Engine</p>
        </div>

        <div class="main-content">
            <!-- System Metrics -->
            <div class="section">
                <h2>System Metrics</h2>
                <div class="metrics-grid" id="metricsGrid">
                    <div class="metric-card">
                        <div class="metric-value" id="totalModels">0</div>
                        <div class="metric-label">Models Loaded</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="totalRequests">0</div>
                        <div class="metric-label">Total Requests</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="avgLatency">0.0s</div>
                        <div class="metric-label">Avg Latency</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="gpuMemory">0%</div>
                        <div class="metric-label">GPU Memory (Avg)</div>
                    </div>
                </div>

                <!-- GPU Details -->
                <div class="section">
                    <h2>GPU Status</h2>
                    <div id="gpuDetails" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                        <div style="text-align: center; color: #666; padding: 20px;">
                            Loading GPU information...
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loaded Models -->
            <div class="section">
                <h2>Loaded Models</h2>
                <div style="margin-bottom: 10px; display: flex; gap: 10px; align-items: center;">
                    <button onclick="loadModelList()" style="background: #17a2b8; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                        🔄 Refresh Models
                    </button>
                    <button onclick="unloadAllModels()" style="background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;" id="unloadAllBtn">
                        🗑️ Unload All Models
                    </button>
                    <span id="modelCount" style="color: #666; font-size: 0.9em;"></span>
                </div>
                <div id="modelList">
                    <div style="text-align: center; color: #666; padding: 20px;">
                        No models loaded
                    </div>
                </div>
            </div>

            <!-- Engine Status -->
            <div class="section">
                <h2>vLLM Engine Status</h2>
                <div style="margin-bottom: 10px;">
                    <button onclick="loadEngineStatus()" style="background: #17a2b8; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                        🔄 Refresh Engine Status
                    </button>
                </div>
                <div id="engineStatus">
                    <div style="text-align: center; color: #666; padding: 20px;">
                        Loading engine status...
                    </div>
                </div>
            </div>

            <!-- Local Models Scanner -->
            <div class="section full-width">
                <h2>📁 Local Models Scanner</h2>
                <div style="display: flex; gap: 10px; margin-bottom: 15px; align-items: center;">
                    <button onclick="scanLocalModels()" id="scanButton" style="background: #28a745;">
                        🔍 Scan Local Models
                    </button>
                    <span id="scanStatus" style="color: #666; font-size: 0.9em;"></span>
                </div>

                <div id="localModelsList" style="display: none;">
                    <h3>Available Local Models</h3>
                    <div id="localModelsGrid" style="display: grid; gap: 10px;">
                        <!-- Scanned models will appear here -->
                    </div>
                </div>
            </div>

            <!-- Load New Model -->
            <div class="section full-width">
                <h2>Load New Model</h2>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <input type="text" id="modelName" placeholder="Model Name (e.g., llama2-7b)">
                    <input type="text" id="modelPath" placeholder="Model Path or HuggingFace ID">
                </div>

                <button class="toggle-advanced" onclick="toggleAdvanced()">Advanced Options</button>

                <div class="advanced-options" id="advancedOptions">
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                        <div>
                            <label>GPU Memory Utilization:</label>
                            <input type="number" id="gpuMemory" value="0.3" min="0.1" max="1.0" step="0.1">
                            <small style="display: block; color: #666; margin-top: 4px;">
                                💡 Recommended: 0.3 for RTX 2080 Ti (Turing architecture)
                            </small>
                        </div>
                        <div>
                            <label>Max Model Length:</label>
                            <input type="number" id="maxModelLen" placeholder="Auto">
                        </div>
                        <div>
                            <label>Tensor Parallel Size:</label>
                            <select id="tensorParallel">
                                <option value="1">1 GPU (Single)</option>
                                <option value="2" selected>2 GPUs (Recommended)</option>
                                <option value="4">4 GPUs</option>
                                <option value="8">8 GPUs</option>
                            </select>
                            <small style="display: block; color: #666; margin-top: 4px;">
                                💡 Use 2 GPUs to utilize both RTX 2080 Ti cards (~22GB total VRAM)
                            </small>
                        </div>
                    </div>
                </div>

                <button onclick="loadModel()" id="loadButton">Load Model</button>
            </div>

            <!-- Text Generation -->
            <div class="section full-width">
                <h2>Text Generation</h2>
                <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 20px;">
                    <div>
                        <label>Select Model:</label>
                        <select id="selectedModel">
                            <option value="">No models loaded</option>
                        </select>

                        <label>Generation Parameters:</label>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            <div>
                                <label>Max Tokens:</label>
                                <input type="number" id="maxTokens" value="2048" min="1">
                            </div>
                            <div>
                                <label>Temperature:</label>
                                <input type="number" id="temperature" value="0.7" min="0" max="2" step="0.1">
                            </div>
                        </div>
                        <div>
                            <label>Top P:</label>
                            <input type="number" id="topP" value="0.9" min="0" max="1" step="0.1">
                        </div>

                        <button onclick="generateText()" id="generateButton">Generate Text</button>
                        <button onclick="clearResponse()" style="background: #6c757d;">Clear</button>
                    </div>

                    <div>
                        <label>Prompt:</label>
                        <textarea id="promptText" rows="6" placeholder="Enter your prompt here..."></textarea>
                        
                        <label>Response:</label>
                        <div id="responseArea">Generated text will appear here...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let metricsInterval;

        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 vLLM MAAS Platform - Initializing...');

            // Test basic connectivity first
            testConnectivity().then(connected => {
                if (connected) {
                    console.log('✅ Server connectivity confirmed');
                    loadModelList();
                    loadMetrics();
                    loadEngineStatus();
                    startMetricsUpdates();
                } else {
                    console.error('❌ Server connectivity failed');
                    showConnectivityError();
                }
            });
        });

        async function testConnectivity() {
            try {
                console.log('🔍 Testing server connectivity...');
                const response = await fetch('/engines', {
                    method: 'GET',
                    timeout: 5000,
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ Server connectivity test passed:', data);
                    return true;
                } else {
                    console.error('❌ Server returned error:', response.status);
                    return false;
                }
            } catch (error) {
                console.error('🔌 Connectivity test failed:', error);
                return false;
            }
        }

        function showConnectivityError() {
            const statusDiv = document.getElementById('engineStatus');
            if (statusDiv) {
                statusDiv.innerHTML = `
                    <div style="color: #dc3545; padding: 20px; text-align: center; border: 2px solid #dc3545; border-radius: 8px; background: #f8d7da;">
                        <div style="font-size: 1.2em; font-weight: bold; margin-bottom: 10px;">
                            🚨 Server Connection Failed
                        </div>
                        <div style="margin-bottom: 15px;">
                            Cannot connect to the vLLM MAAS Platform server.
                        </div>
                        <div style="margin-bottom: 15px;">
                            <button onclick="window.location.reload()" style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                                🔄 Retry Connection
                            </button>
                            <button onclick="testConnectivity().then(c => c ? window.location.reload() : showNotification('Still cannot connect to server', 'error'))" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
                                🔍 Test Again
                            </button>
                        </div>
                        <div style="font-size: 0.9em; color: #721c24;">
                            💡 Make sure the server is running: <code>./start.sh</code>
                        </div>
                    </div>
                `;
            }
        }

        function startMetricsUpdates() {
            metricsInterval = setInterval(() => {
                loadMetrics();
                loadEngineStatus();
            }, 5000); // Update every 5 seconds
        }

        async function loadEngineStatus(retryCount = 0) {
            const statusDiv = document.getElementById('engineStatus');
            const maxRetries = 3;
            const startTime = Date.now();

            try {
                // Show loading state with retry info
                if (statusDiv) {
                    const retryText = retryCount > 0 ? ` (Retry ${retryCount}/${maxRetries})` : '';
                    statusDiv.innerHTML = `<div style="text-align: center; color: #666; padding: 20px;">
                        Loading engine status${retryText}...
                        <div style="font-size: 0.8em; margin-top: 5px; opacity: 0.7;">
                            🔍 Checking vLLM engine availability
                        </div>
                    </div>`;
                }

                // Add timeout to fetch request
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

                console.log(`🔍 Loading engine status (attempt ${retryCount + 1})`);

                const response = await fetch('/engines', {
                    signal: controller.signal,
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                clearTimeout(timeoutId);
                const responseTime = Date.now() - startTime;

                console.log(`✅ Engine status response: ${response.status} (${responseTime}ms)`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const engines = await response.json();
                console.log('📋 Engine data:', engines);

                if (!statusDiv) {
                    console.error('engineStatus element not found!');
                    return;
                }

                // Clear loading state
                statusDiv.innerHTML = '';

                // Check if engines data is valid
                if (!engines || typeof engines !== 'object') {
                    throw new Error('Invalid engines data received');
                }

                // Process each engine
                Object.entries(engines).forEach(([engine, available]) => {
                    const engineDiv = document.createElement('div');
                    engineDiv.style.cssText = 'display: flex; justify-content: space-between; align-items: center; padding: 12px; margin: 6px 0; border: 1px solid #eee; border-radius: 6px; background: #f8f9fa;';

                    const statusColor = available ? '#28a745' : '#dc3545';
                    const statusText = available ? 'Available' : 'Not Available';
                    const statusIcon = available ? '✅' : '❌';

                    engineDiv.innerHTML = `
                        <span style="font-weight: bold; text-transform: capitalize;">
                            ${statusIcon} ${engine} Engine
                        </span>
                        <span style="color: ${statusColor}; font-weight: bold; padding: 4px 8px; border-radius: 12px; background: ${statusColor}20;">
                            ${statusText}
                        </span>
                    `;

                    statusDiv.appendChild(engineDiv);
                });

                // Add success timestamp
                const timestampDiv = document.createElement('div');
                timestampDiv.style.cssText = 'text-align: center; font-size: 0.8em; color: #666; margin-top: 10px; padding: 5px;';
                timestampDiv.innerHTML = `
                    🕒 Last updated: ${new Date().toLocaleTimeString()} (${responseTime}ms)
                `;
                statusDiv.appendChild(timestampDiv);

            } catch (error) {
                const responseTime = Date.now() - startTime;
                console.error('❌ Error loading engine status:', error);
                console.error('📊 Response time:', responseTime + 'ms');
                console.error('🔍 Error details:', {
                    name: error.name,
                    message: error.message,
                    stack: error.stack
                });

                // Retry logic
                if (retryCount < maxRetries && (error.name === 'AbortError' || error.message.includes('fetch') || error.message.includes('NetworkError'))) {
                    console.log(`🔄 Retrying engine status load (${retryCount + 1}/${maxRetries}) in ${2 * (retryCount + 1)}s...`);
                    setTimeout(() => {
                        loadEngineStatus(retryCount + 1);
                    }, 2000 * (retryCount + 1)); // Exponential backoff
                    return;
                }

                if (statusDiv) {
                    const errorMessage = error.name === 'AbortError' ? 'Request timeout (>15s)' : error.message;
                    const errorDetails = error.name === 'AbortError' ?
                        'The server is taking too long to respond. This might indicate high load.' :
                        'Check browser console for more details.';

                    statusDiv.innerHTML = `
                        <div style="color: #dc3545; padding: 15px; text-align: center; border: 1px solid #dc3545; border-radius: 6px; background: #f8d7da;">
                            <div style="font-weight: bold; margin-bottom: 8px;">
                                ❌ Engine Status Error
                            </div>
                            <div style="margin-bottom: 8px;">
                                ${errorMessage}
                            </div>
                            <div style="font-size: 0.9em; color: #721c24; margin-bottom: 10px;">
                                ${errorDetails}
                            </div>
                            <div style="margin-bottom: 10px;">
                                <button onclick="loadEngineStatus(0)" style="background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 8px;">
                                    🔄 Retry Now
                                </button>
                                <button onclick="window.location.reload()" style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                                    🔄 Refresh Page
                                </button>
                            </div>
                            <div style="font-size: 0.8em; color: #721c24;">
                                💡 Try: Hard refresh (Ctrl+Shift+R) or clear browser cache
                            </div>
                        </div>
                    `;
                }
            }
        }

        async function scanLocalModels() {
            const scanButton = document.getElementById('scanButton');
            const scanStatus = document.getElementById('scanStatus');
            const localModelsList = document.getElementById('localModelsList');
            const localModelsGrid = document.getElementById('localModelsGrid');

            // Check if elements exist
            if (!scanButton || !scanStatus || !localModelsList || !localModelsGrid) {
                console.error('Required DOM elements not found:', {
                    scanButton: !!scanButton,
                    scanStatus: !!scanStatus,
                    localModelsList: !!localModelsList,
                    localModelsGrid: !!localModelsGrid
                });
                return;
            }

            scanButton.disabled = true;
            scanButton.textContent = '🔄 Scanning...';
            scanStatus.textContent = 'Scanning local models directory...';
            scanStatus.style.color = '#666'; // Reset color

            try {
                console.log('Fetching /models/scan...');
                const response = await fetch('/models/scan');
                console.log('Response status:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Scan response data:', data);

                if (response.ok) {
                    const models = data.models;
                    if (!Array.isArray(models)) {
                        throw new Error('Invalid response: models is not an array');
                    }

                    scanStatus.textContent = `Found ${models.length} model(s)`;
                    scanStatus.style.color = '#28a745';

                    if (models.length > 0) {
                        localModelsList.style.display = 'block';
                        localModelsGrid.innerHTML = '';

                        models.forEach((model, index) => {
                            try {
                                console.log(`Creating card for model ${index}:`, model);
                                const modelCard = createLocalModelCard(model);
                                localModelsGrid.appendChild(modelCard);
                            } catch (cardError) {
                                console.error(`Error creating card for model ${index}:`, cardError);
                                scanStatus.textContent = `Error creating model card: ${cardError.message}`;
                                scanStatus.style.color = '#dc3545';
                            }
                        });

                        console.log('Successfully created all model cards');
                    } else {
                        localModelsList.style.display = 'none';
                        scanStatus.textContent = 'No models found in local directory';
                        scanStatus.style.color = '#ffc107';
                    }
                } else {
                    const errorMsg = data.detail || 'Unknown error';
                    scanStatus.textContent = `Error: ${errorMsg}`;
                    scanStatus.style.color = '#dc3545';
                }

            } catch (error) {
                console.error('Error scanning models:', error);
                scanStatus.textContent = `Error scanning models: ${error.message}`;
                scanStatus.style.color = '#dc3545';
            } finally {
                scanButton.disabled = false;
                scanButton.textContent = '🔍 Scan Local Models';
            }
        }

        function createLocalModelCard(model) {
            try {
                // Validate model data
                if (!model || typeof model !== 'object') {
                    throw new Error('Invalid model data');
                }

                const requiredFields = ['name', 'path', 'size_gb', 'model_type', 'is_valid'];
                for (const field of requiredFields) {
                    if (!(field in model)) {
                        throw new Error(`Missing required field: ${field}`);
                    }
                }

                const card = document.createElement('div');
                card.className = `local-model-card ${!model.is_valid ? 'model-invalid' : ''}`;

                const validityIcon = model.is_valid ? '✅' : '⚠️';
                const validityText = model.is_valid ? 'Valid' : 'Invalid/Incomplete';

                // Safely access config_info
                const configInfo = model.config_info || {};
                const layers = configInfo.num_hidden_layers || 'Unknown';
                const hiddenSize = configInfo.hidden_size || 'Unknown';

                // Escape strings to prevent XSS
                const escapedName = escapeHtml(model.name);
                const escapedPath = escapeHtml(model.path);
                const escapedType = escapeHtml(model.model_type);

                card.innerHTML = `
                    <div class="local-model-header">
                        <div class="local-model-name">${validityIcon} ${escapedName}</div>
                        <div class="local-model-type">${escapedType}</div>
                    </div>

                    <div class="local-model-details">
                        <div class="local-model-detail">
                            <span class="local-model-detail-label">Size</span>
                            <span>${model.size_gb} GB</span>
                        </div>
                        <div class="local-model-detail">
                            <span class="local-model-detail-label">Status</span>
                            <span style="color: ${model.is_valid ? '#28a745' : '#dc3545'}">${validityText}</span>
                        </div>
                        <div class="local-model-detail">
                            <span class="local-model-detail-label">Layers</span>
                            <span>${layers}</span>
                        </div>
                        <div class="local-model-detail">
                            <span class="local-model-detail-label">Hidden Size</span>
                            <span>${hiddenSize}</span>
                        </div>
                    </div>

                    <div style="font-size: 0.8em; color: #666; margin: 8px 0;">
                        <strong>Path:</strong> ${escapedPath}
                    </div>

                    <div class="local-model-actions">
                        ${model.is_valid ? `
                            <button class="load-model-btn" onclick="loadModelWithProgress('${escapedName}', '${escapedPath}')" id="load-btn-${escapedName}">
                                🚀 Load Model
                            </button>
                            <div class="progress-container" id="progress-${escapedName}">
                                <div class="progress-bar">
                                    <div class="progress-fill" id="progress-fill-${escapedName}">
                                        <div class="progress-text" id="progress-text-${escapedName}">0%</div>
                                    </div>
                                </div>
                                <div class="progress-message" id="progress-message-${escapedName}">Ready to load</div>
                                <div class="progress-stage" id="progress-stage-${escapedName}">Waiting</div>
                            </div>
                        ` : `
                            <button disabled style="background: #6c757d !important; cursor: not-allowed;">
                                ❌ Cannot Load (Invalid)
                            </button>
                        `}
                        <button onclick="fillModelForm('${escapedName}', '${escapedPath}')" style="background: #ffc107; color: #000;">
                            📝 Fill Form
                        </button>
                        <button onclick="copyModelPath('${escapedPath}')" style="background: #17a2b8;">
                            📋 Copy Path
                        </button>
                    </div>
                `;

                return card;

            } catch (error) {
                console.error('Error creating model card:', error);

                // Return a simple error card
                const errorCard = document.createElement('div');
                errorCard.className = 'local-model-card model-invalid';
                errorCard.innerHTML = `
                    <div style="color: #dc3545; padding: 15px; text-align: center;">
                        ❌ Error creating model card: ${error.message}
                    </div>
                `;
                return errorCard;
            }
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        async function loadModelWithProgress(modelName, modelPath) {
            const button = document.getElementById(`load-btn-${modelName}`);
            const progressContainer = document.getElementById(`progress-${modelName}`);
            const progressFill = document.getElementById(`progress-fill-${modelName}`);
            const progressText = document.getElementById(`progress-text-${modelName}`);
            const progressMessage = document.getElementById(`progress-message-${modelName}`);
            const progressStage = document.getElementById(`progress-stage-${modelName}`);

            const originalText = button.textContent;

            try {
                // Update button state and show progress
                button.disabled = true;
                button.textContent = '⏳ Loading...';
                button.style.background = '#ffc107';
                progressContainer.style.display = 'block';

                // Prepare load request for Unsloth
                const loadRequest = {
                    model_name: modelName,
                    model_path: modelPath,
                    max_seq_length: 2048,
                    load_in_4bit: true,
                    trust_remote_code: false
                };

                console.log('Loading model with progress:', loadRequest);

                // Start loading
                const loadPromise = fetch('/models/load', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(loadRequest)
                });

                // Start progress monitoring
                const progressInterval = setInterval(async () => {
                    try {
                        const progressResponse = await fetch(`/models/loading-progress/${modelName}`);
                        if (progressResponse.ok) {
                            const progress = await progressResponse.json();
                            updateProgressDisplay(progress, progressFill, progressText, progressMessage, progressStage);
                        }
                    } catch (error) {
                        console.log('Progress monitoring error (expected during completion):', error);
                    }
                }, 500); // Update every 500ms

                // Wait for loading to complete
                const response = await loadPromise;
                const result = await response.json();

                // Stop progress monitoring
                clearInterval(progressInterval);

                if (response.ok) {
                    // Show completion
                    updateProgressDisplay({
                        progress: 100,
                        stage: 'complete',
                        message: 'Model loaded successfully!',
                        elapsed_time: 0
                    }, progressFill, progressText, progressMessage, progressStage);

                    button.textContent = '✅ Loaded!';
                    button.style.background = '#28a745';

                    showNotification(`Model ${modelName} loaded successfully with Unsloth optimizations!`, 'success');

                    // Refresh model list after a delay
                    setTimeout(() => {
                        loadModelList();
                        progressContainer.style.display = 'none';
                    }, 3000);

                } else {
                    throw new Error(result.detail || 'Failed to load model');
                }

            } catch (error) {
                console.error('Error loading model:', error);

                // Show error state
                progressFill.style.background = 'linear-gradient(90deg, #dc3545, #c82333)';
                progressText.textContent = 'Error';
                progressMessage.textContent = error.message;
                progressStage.textContent = 'Failed';

                button.textContent = '❌ Failed';
                button.style.background = '#dc3545';

                showNotification(`Failed to load model: ${error.message}`, 'error');

            } finally {
                // Reset button after delay
                setTimeout(() => {
                    button.disabled = false;
                    button.textContent = originalText;
                    button.style.background = '#28a745';
                    progressContainer.style.display = 'none';

                    // Reset progress display
                    progressFill.style.width = '0%';
                    progressFill.style.background = 'linear-gradient(90deg, #28a745, #20c997)';
                    progressText.textContent = '0%';
                    progressMessage.textContent = 'Ready to load';
                    progressStage.textContent = 'Waiting';
                }, 5000);
            }
        }

        function updateProgressDisplay(progress, progressFill, progressText, progressMessage, progressStage) {
            const percentage = Math.min(100, Math.max(0, progress.progress || 0));

            progressFill.style.width = `${percentage}%`;
            progressText.textContent = `${percentage}%`;
            progressMessage.textContent = progress.message || 'Loading...';

            // Update stage with emoji
            const stageEmojis = {
                'initializing': '🔄 Initializing',
                'validating': '✅ Validating',
                'preparing': '⚙️ Preparing',
                'gpu_setup': '🎮 GPU Setup',
                'loading': '📦 Loading Model',
                'optimizing': '⚡ Optimizing',
                'finalizing': '🔧 Finalizing',
                'completing': '📝 Completing',
                'complete': '🎉 Complete'
            };

            progressStage.textContent = stageEmojis[progress.stage] || progress.stage || 'Loading...';

            // Add elapsed time if available
            if (progress.elapsed_time) {
                progressStage.textContent += ` (${Math.round(progress.elapsed_time)}s)`;
            }
        }

        function fillModelForm(modelName, modelPath) {
            // Fill the form with model information
            document.getElementById('modelName').value = modelName;
            document.getElementById('modelPath').value = modelPath;

            // Scroll to the load model section
            const loadSection = document.querySelector('h2:contains("Load New Model")') ||
                               Array.from(document.querySelectorAll('h2')).find(h => h.textContent.includes('Load New Model'));

            if (loadSection) {
                loadSection.scrollIntoView({ behavior: 'smooth' });

                // Highlight the form briefly
                const section = loadSection.parentElement;
                section.style.border = '2px solid #ffc107';
                setTimeout(() => {
                    section.style.border = '1px solid #e0e0e0';
                }, 2000);
            }

            showNotification(`Form filled with ${modelName} information`, 'info');
        }

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                z-index: 10000;
                max-width: 400px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                transition: all 0.3s ease;
            `;

            // Set color based on type
            const colors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8'
            };

            notification.style.background = colors[type] || colors.info;
            notification.textContent = message;

            // Add to page
            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 5000);
        }

        function copyModelPath(path) {
            const button = event.target;
            const originalText = button.textContent;

            // Try modern clipboard API first
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(path).then(() => {
                    showCopySuccess(button, originalText);
                }).catch(err => {
                    console.error('Clipboard API failed:', err);
                    fallbackCopyToClipboard(path, button, originalText);
                });
            } else {
                // Fallback for older browsers or non-secure contexts
                fallbackCopyToClipboard(path, button, originalText);
            }
        }

        function fallbackCopyToClipboard(text, button, originalText) {
            try {
                // Create a temporary textarea element
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);

                // Select and copy the text
                textArea.focus();
                textArea.select();

                const successful = document.execCommand('copy');
                document.body.removeChild(textArea);

                if (successful) {
                    showCopySuccess(button, originalText);
                } else {
                    showCopyError(button, originalText, text);
                }
            } catch (err) {
                console.error('Fallback copy failed:', err);
                showCopyError(button, originalText, text);
            }
        }

        function showCopySuccess(button, originalText) {
            button.textContent = '✅ Copied!';
            button.style.background = '#28a745';

            setTimeout(() => {
                button.textContent = originalText;
                button.style.background = '#17a2b8';
            }, 1500);
        }

        function showCopyError(button, originalText, path) {
            // Show error state
            button.textContent = '❌ Failed';
            button.style.background = '#dc3545';

            // Show a modal with the path for manual copying
            showPathModal(path);

            setTimeout(() => {
                button.textContent = originalText;
                button.style.background = '#17a2b8';
            }, 2000);
        }

        function showPathModal(path) {
            // Create modal overlay
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;

            // Create modal content
            const modal = document.createElement('div');
            modal.style.cssText = `
                background: white;
                padding: 20px;
                border-radius: 8px;
                max-width: 80%;
                max-height: 80%;
                overflow: auto;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            `;

            modal.innerHTML = `
                <h3 style="margin-top: 0; color: #333;">📋 Copy Model Path</h3>
                <p style="color: #666; margin-bottom: 15px;">
                    Automatic copy failed. Please manually copy the path below:
                </p>
                <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; border: 1px solid #ddd; margin-bottom: 15px;">
                    <code style="word-break: break-all; font-family: monospace;">${path}</code>
                </div>
                <div style="text-align: right;">
                    <button onclick="this.closest('.modal-overlay').remove()"
                            style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                        Close
                    </button>
                </div>
            `;

            overlay.className = 'modal-overlay';
            overlay.appendChild(modal);
            document.body.appendChild(overlay);

            // Close modal when clicking overlay
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    overlay.remove();
                }
            });

            // Auto-select the path text for easy copying
            const codeElement = modal.querySelector('code');
            if (codeElement) {
                const range = document.createRange();
                range.selectNodeContents(codeElement);
                const selection = window.getSelection();
                selection.removeAllRanges();
                selection.addRange(range);
            }
        }

        async function loadModelList() {
            try {
                const response = await fetch('/models/info');
                const models = await response.json();

                const modelListDiv = document.getElementById('modelList');
                const selectedModelSelect = document.getElementById('selectedModel');

                // Update model count
                const modelCountSpan = document.getElementById('modelCount');
                const loadedCount = models.filter(m => m.status === 'loaded').length;
                const totalCount = models.length;

                if (modelCountSpan) {
                    if (totalCount === 0) {
                        modelCountSpan.textContent = 'No models loaded';
                    } else {
                        modelCountSpan.textContent = `${loadedCount} loaded / ${totalCount} total`;
                    }
                }

                // Update unload all button state
                const unloadAllBtn = document.getElementById('unloadAllBtn');
                if (unloadAllBtn) {
                    unloadAllBtn.disabled = loadedCount === 0;
                    unloadAllBtn.style.opacity = loadedCount === 0 ? '0.5' : '1';
                }

                if (models.length === 0) {
                    modelListDiv.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">No models loaded</div>';
                    selectedModelSelect.innerHTML = '<option value="">No models loaded</option>';
                    return;
                }

                // Update model list display
                modelListDiv.innerHTML = '';
                models.forEach(model => {
                    const statusClass = model.status === 'loaded' ? 'status-loaded' :
                                      model.status === 'loading' ? 'status-loading' : 'status-error';

                    const modelDiv = document.createElement('div');
                    modelDiv.className = 'model-item';
                    modelDiv.innerHTML = `
                        <div class="model-header">
                            <div>
                                <span class="status-indicator ${statusClass}"></span>
                                ${model.name}
                                <span style="background: #667eea; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em; margin-left: 8px;">
                                    vllm
                                </span>
                            </div>
                            <div class="model-details">
                                Status: ${model.status} |
                                Engine: vllm |
                                Requests: ${model.total_requests} |
                                Avg Latency: ${model.avg_latency.toFixed(2)}s
                            </div>
                            <button onclick="unloadModel('${model.name}')"
                                    style="background: #dc3545; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 0.85em; cursor: pointer; transition: all 0.3s ease;"
                                    onmouseover="this.style.background='#c82333'"
                                    onmouseout="this.style.background='#dc3545'"
                                    title="Unload this model to free GPU memory">
                                🗑️ Unload
                            </button>
                        </div>
                    `;
                    modelListDiv.appendChild(modelDiv);
                });

                // Update model selection dropdown
                selectedModelSelect.innerHTML = '<option value="">Select a model</option>';
                models.filter(m => m.status === 'loaded').forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.name;
                    option.textContent = model.name;
                    selectedModelSelect.appendChild(option);
                });

            } catch (error) {
                console.error('Error loading model list:', error);
                document.getElementById('modelList').innerHTML =
                    '<div style="color: #dc3545;">Error loading models</div>';
            }
        }

        async function loadMetrics() {
            try {
                const response = await fetch('/metrics');
                const metrics = await response.json();

                document.getElementById('totalModels').textContent = metrics.total_models || 0;
                document.getElementById('totalRequests').textContent = metrics.total_requests || 0;
                document.getElementById('avgLatency').textContent = (metrics.avg_latency || 0).toFixed(2) + 's';
                document.getElementById('gpuMemory').textContent = (metrics.gpu_memory_usage || 0).toFixed(1) + '%';

                // Update GPU details
                updateGPUDetails(metrics.raw_metrics);

            } catch (error) {
                console.error('Error loading metrics:', error);
            }
        }

        function updateGPUDetails(rawMetrics) {
            const gpuDetailsDiv = document.getElementById('gpuDetails');
            if (!gpuDetailsDiv || !rawMetrics || !rawMetrics.gpu || !rawMetrics.gpu.gpus) {
                return;
            }

            const gpus = rawMetrics.gpu.gpus;
            gpuDetailsDiv.innerHTML = '';

            gpus.forEach((gpu, index) => {
                const gpuCard = document.createElement('div');
                gpuCard.style.cssText = `
                    background: #f8f9fa;
                    border: 1px solid #e0e0e0;
                    border-radius: 8px;
                    padding: 15px;
                    transition: all 0.3s ease;
                `;

                const memoryUsedGB = (gpu.memory_used / (1024 ** 3)).toFixed(1);
                const memoryTotalGB = (gpu.memory_total / (1024 ** 3)).toFixed(1);
                const memoryFreeGB = (gpu.memory_free / (1024 ** 3)).toFixed(1);

                // Color coding based on memory usage
                const memoryPercent = gpu.memory_percent;
                let memoryColor = '#28a745'; // Green
                if (memoryPercent > 70) memoryColor = '#ffc107'; // Yellow
                if (memoryPercent > 85) memoryColor = '#dc3545'; // Red

                gpuCard.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <h4 style="margin: 0; color: #333;">🎮 GPU ${gpu.gpu_id} (RTX 2080 Ti)</h4>
                        <span style="background: ${memoryColor}; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8em; font-weight: bold;">
                            ${memoryPercent.toFixed(1)}%
                        </span>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 0.9em;">
                        <div>
                            <strong>Memory:</strong><br>
                            <span style="color: #666;">Used: ${memoryUsedGB}GB</span><br>
                            <span style="color: #666;">Free: ${memoryFreeGB}GB</span><br>
                            <span style="color: #666;">Total: ${memoryTotalGB}GB</span>
                        </div>
                        <div>
                            <strong>Status:</strong><br>
                            <span style="color: #666;">Utilization: ${gpu.gpu_utilization}%</span><br>
                            <span style="color: #666;">Temperature: ${gpu.temperature}°C</span><br>
                            <span style="color: #666;">Power: ${gpu.power_usage.toFixed(1)}W</span>
                        </div>
                    </div>

                    <div style="margin-top: 10px; background: #e9ecef; border-radius: 4px; height: 8px; overflow: hidden;">
                        <div style="background: ${memoryColor}; height: 100%; width: ${memoryPercent}%; transition: width 0.3s ease;"></div>
                    </div>
                `;

                gpuDetailsDiv.appendChild(gpuCard);
            });

            // Add total memory info
            const totalCard = document.createElement('div');
            totalCard.style.cssText = `
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 8px;
                padding: 15px;
                text-align: center;
            `;

            const totalMemoryGB = gpus.reduce((sum, gpu) => sum + gpu.memory_total, 0) / (1024 ** 3);
            const totalUsedGB = gpus.reduce((sum, gpu) => sum + gpu.memory_used, 0) / (1024 ** 3);
            const totalFreeGB = gpus.reduce((sum, gpu) => sum + gpu.memory_free, 0) / (1024 ** 3);

            totalCard.innerHTML = `
                <h4 style="margin: 0 0 10px 0;">💎 Total GPU Resources</h4>
                <div style="font-size: 1.1em; font-weight: bold;">
                    ${totalFreeGB.toFixed(1)}GB Available
                </div>
                <div style="font-size: 0.9em; opacity: 0.9; margin-top: 5px;">
                    ${totalUsedGB.toFixed(1)}GB Used / ${totalMemoryGB.toFixed(1)}GB Total
                </div>
                <div style="margin-top: 8px; font-size: 0.8em; opacity: 0.8;">
                    🚀 Perfect for large models with tensor_parallel_size=2
                </div>
            `;

            gpuDetailsDiv.appendChild(totalCard);
        }

        async function loadModel() {
            const modelName = document.getElementById('modelName').value.trim();
            const modelPath = document.getElementById('modelPath').value.trim();

            if (!modelName || !modelPath) {
                alert('Please enter both model name and path');
                return;
            }

            const loadButton = document.getElementById('loadButton');
            loadButton.disabled = true;
            loadButton.textContent = 'Loading...';

            try {
                const requestData = {
                    model_name: modelName,
                    model_path: modelPath,
                    gpu_memory_utilization: parseFloat(document.getElementById('gpuMemory').value),
                    tensor_parallel_size: parseInt(document.getElementById('tensorParallel').value)
                };

                const maxModelLen = document.getElementById('maxModelLen').value.trim();
                if (maxModelLen) {
                    requestData.max_model_len = parseInt(maxModelLen);
                }

                const response = await fetch('/models/load', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();

                if (response.ok) {
                    alert(`Model loading started: ${result.message}`);
                    // Clear form
                    document.getElementById('modelName').value = '';
                    document.getElementById('modelPath').value = '';

                    // Refresh model list
                    setTimeout(() => {
                        loadModelList();
                    }, 2000);
                } else {
                    alert(`Error: ${result.detail}`);
                }

            } catch (error) {
                console.error('Error loading model:', error);
                alert('Error loading model. Please check the console for details.');
            } finally {
                loadButton.disabled = false;
                loadButton.textContent = 'Load Model';
            }
        }

        async function unloadModel(modelName) {
            if (!confirm(`🗑️ Are you sure you want to unload model "${modelName}"?\n\nThis will free up GPU memory but you'll need to reload the model to use it again.`)) {
                return;
            }

            // Find the unload button for this model
            const unloadButton = document.querySelector(`button[onclick="unloadModel('${modelName}')"]`);
            const originalText = unloadButton ? unloadButton.textContent : 'Unload';

            try {
                // Update button state
                if (unloadButton) {
                    unloadButton.disabled = true;
                    unloadButton.textContent = '⏳ Unloading...';
                    unloadButton.style.background = '#ffc107';
                }

                console.log(`🗑️ Unloading model: ${modelName}`);

                const response = await fetch(`/models/${modelName}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (response.ok) {
                    if (unloadButton) {
                        unloadButton.textContent = '✅ Unloaded';
                        unloadButton.style.background = '#28a745';
                    }

                    showNotification(`Model "${modelName}" unloaded successfully! GPU memory freed.`, 'success');
                    console.log(`✅ Model ${modelName} unloaded successfully`);

                    // Refresh model list and metrics
                    setTimeout(() => {
                        loadModelList();
                        loadMetrics();
                    }, 1000);

                } else {
                    throw new Error(result.detail || 'Failed to unload model');
                }

            } catch (error) {
                console.error('❌ Error unloading model:', error);

                if (unloadButton) {
                    unloadButton.textContent = '❌ Failed';
                    unloadButton.style.background = '#dc3545';
                }

                showNotification(`Failed to unload model "${modelName}": ${error.message}`, 'error');

            } finally {
                // Reset button after delay
                if (unloadButton) {
                    setTimeout(() => {
                        unloadButton.disabled = false;
                        unloadButton.textContent = originalText;
                        unloadButton.style.background = '#dc3545';
                    }, 3000);
                }
            }
        }

        async function unloadAllModels() {
            try {
                // Get current models
                const response = await fetch('/models/info');
                const models = await response.json();

                const loadedModels = models.filter(m => m.status === 'loaded');

                if (loadedModels.length === 0) {
                    showNotification('No models are currently loaded.', 'info');
                    return;
                }

                const modelNames = loadedModels.map(m => m.name).join(', ');
                if (!confirm(`🗑️ Are you sure you want to unload ALL models?\n\nModels to unload: ${modelNames}\n\nThis will free up all GPU memory.`)) {
                    return;
                }

                const unloadAllBtn = document.getElementById('unloadAllBtn');
                const originalText = unloadAllBtn.textContent;

                // Update button state
                unloadAllBtn.disabled = true;
                unloadAllBtn.textContent = '⏳ Unloading All...';
                unloadAllBtn.style.background = '#ffc107';

                console.log(`🗑️ Unloading ${loadedModels.length} models...`);

                let successCount = 0;
                let failCount = 0;

                // Unload models one by one
                for (const model of loadedModels) {
                    try {
                        const unloadResponse = await fetch(`/models/${model.name}`, {
                            method: 'DELETE'
                        });

                        if (unloadResponse.ok) {
                            successCount++;
                            console.log(`✅ Unloaded: ${model.name}`);
                        } else {
                            failCount++;
                            console.error(`❌ Failed to unload: ${model.name}`);
                        }
                    } catch (error) {
                        failCount++;
                        console.error(`❌ Error unloading ${model.name}:`, error);
                    }
                }

                // Update button and show results
                if (failCount === 0) {
                    unloadAllBtn.textContent = '✅ All Unloaded';
                    unloadAllBtn.style.background = '#28a745';
                    showNotification(`Successfully unloaded all ${successCount} models! GPU memory freed.`, 'success');
                } else {
                    unloadAllBtn.textContent = '⚠️ Partial Success';
                    unloadAllBtn.style.background = '#ffc107';
                    showNotification(`Unloaded ${successCount} models, ${failCount} failed. Check console for details.`, 'warning');
                }

                // Refresh model list and metrics
                setTimeout(() => {
                    loadModelList();
                    loadMetrics();
                }, 1000);

            } catch (error) {
                console.error('❌ Error in unload all models:', error);
                showNotification(`Failed to unload models: ${error.message}`, 'error');

                const unloadAllBtn = document.getElementById('unloadAllBtn');
                if (unloadAllBtn) {
                    unloadAllBtn.textContent = '❌ Failed';
                    unloadAllBtn.style.background = '#dc3545';
                }
            } finally {
                // Reset button after delay
                const unloadAllBtn = document.getElementById('unloadAllBtn');
                if (unloadAllBtn) {
                    setTimeout(() => {
                        unloadAllBtn.disabled = false;
                        unloadAllBtn.textContent = '🗑️ Unload All Models';
                        unloadAllBtn.style.background = '#dc3545';
                    }, 5000);
                }
            }
        }

        async function generateText() {
            const selectedModel = document.getElementById('selectedModel').value;
            const promptText = document.getElementById('promptText').value.trim();

            if (!selectedModel) {
                alert('Please select a model');
                return;
            }

            if (!promptText) {
                alert('Please enter a prompt');
                return;
            }

            const generateButton = document.getElementById('generateButton');
            const responseArea = document.getElementById('responseArea');

            generateButton.disabled = true;
            generateButton.textContent = 'Generating...';
            responseArea.textContent = 'Generating response...';

            try {
                const requestData = {
                    model_name: selectedModel,
                    prompt: promptText,
                    max_tokens: parseInt(document.getElementById('maxTokens').value),
                    temperature: parseFloat(document.getElementById('temperature').value),
                    top_p: parseFloat(document.getElementById('topP').value)
                };

                const response = await fetch('/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();

                if (response.ok) {
                    responseArea.textContent = result.generated_text;

                    // Update metrics
                    setTimeout(() => {
                        loadMetrics();
                        loadModelList();
                    }, 1000);
                } else {
                    responseArea.textContent = `Error: ${result.detail}`;
                }

            } catch (error) {
                console.error('Error generating text:', error);
                responseArea.textContent = 'Error generating text. Please check the console for details.';
            } finally {
                generateButton.disabled = false;
                generateButton.textContent = 'Generate Text';
            }
        }

        function clearResponse() {
            document.getElementById('responseArea').textContent = 'Generated text will appear here...';
            document.getElementById('promptText').value = '';
        }

        function toggleAdvanced() {
            const advancedOptions = document.getElementById('advancedOptions');
            const isVisible = advancedOptions.style.display === 'block';
            advancedOptions.style.display = isVisible ? 'none' : 'block';
        }
    </script>
</body>
</html>
