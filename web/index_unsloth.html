<!DOCTYPE html>
<html>
<head>
    <title>🦥 Unsloth MAAS Platform - Optimized</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * { box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 { 
            margin: 0; 
            font-size: 2.5em; 
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .optimization-badge {
            background: linear-gradient(45deg, #28a745, #20c997);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.6em;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
        }

        .section {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #28a745;
            padding-bottom: 10px;
        }

        .load-model-btn {
            background: linear-gradient(45deg, #28a745, #20c997) !important;
            padding: 12px 24px !important;
            font-size: 1em !important;
            border-radius: 8px !important;
            border: none;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .load-model-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        .load-model-btn:disabled {
            background: #6c757d !important;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .progress-container {
            margin-top: 15px;
            display: none;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .progress-bar {
            width: 100%;
            height: 25px;
            background-color: #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997, #17a2b8);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 12px;
            position: relative;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 13px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
            z-index: 10;
        }

        .progress-message {
            margin-top: 8px;
            font-size: 0.9em;
            color: #495057;
            text-align: center;
            font-weight: 500;
        }

        .progress-stage {
            margin-top: 5px;
            font-size: 0.8em;
            color: #28a745;
            font-weight: bold;
            text-align: center;
            padding: 4px 8px;
            background: rgba(40, 167, 69, 0.1);
            border-radius: 4px;
            display: inline-block;
            width: 100%;
        }

        .model-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #fff;
            transition: all 0.3s ease;
        }

        .model-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .model-info {
            margin-bottom: 10px;
        }

        .model-info strong {
            color: #495057;
        }

        .local-model-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .notification.success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }

        .notification.error {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .feature-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }

        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .feature-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }

        .feature-desc {
            font-size: 0.9em;
            color: #6c757d;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-healthy { background: #28a745; }
        .status-loading { background: #ffc107; animation: pulse 1s infinite; }
        .status-error { background: #dc3545; }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .gpu-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .gpu-card {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 10px;
        }

        .memory-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }

        .memory-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* Chat Interface Styles */
        .chat-section {
            grid-column: 1 / -1;
            margin-top: 20px;
        }

        .chat-container {
            display: flex;
            flex-direction: column;
            height: 600px;
            border: 1px solid #dee2e6;
            border-radius: 12px;
            overflow: hidden;
            background: #fff;
        }

        .chat-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 15px 20px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
            font-weight: bold;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .message.assistant .message-avatar {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .message.system .message-avatar {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }

        .message-content {
            flex: 1;
            background: white;
            padding: 12px 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 70%;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .message-meta {
            font-size: 0.75em;
            color: #6c757d;
            margin-top: 5px;
        }

        .message.user .message-meta {
            color: rgba(255,255,255,0.8);
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #dee2e6;
        }

        .chat-input-form {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            min-height: 40px;
            max-height: 120px;
            padding: 10px 15px;
            border: 2px solid #dee2e6;
            border-radius: 20px;
            resize: none;
            font-family: inherit;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .chat-input:focus {
            outline: none;
            border-color: #28a745;
        }

        .chat-send-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            min-width: 80px;
        }

        .chat-send-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .chat-send-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .chat-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .model-selector {
            padding: 8px 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            background: white;
            font-size: 14px;
        }

        .chat-settings {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .setting-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .setting-input {
            width: 60px;
            padding: 4px 8px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 12px;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 10px;
            padding: 10px 15px;
            color: #6c757d;
            font-style: italic;
        }

        .typing-dots {
            display: flex;
            gap: 3px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background: #6c757d;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                🦥 Unsloth MAAS Platform
                <span class="optimization-badge">Optimized</span>
            </h1>
            <p>Ultra-Fast Model as a Service with Advanced Progress Tracking</p>
            <p>🚀 2x Faster • 💾 75% Memory Savings • ⚡ Real-time Progress</p>
        </div>

        <div class="main-content">
            <div class="section">
                <h2>🎮 System Status</h2>
                <div id="system-status">
                    <div class="status-indicator status-loading"></div>
                    Loading system information...
                </div>
                
                <div id="gpu-info-container"></div>

                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🚀</div>
                        <div class="feature-title">Async Inference</div>
                        <div class="feature-desc">Non-blocking parallel processing</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📦</div>
                        <div class="feature-title">Batch Processing</div>
                        <div class="feature-desc">Multiple prompts simultaneously</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🧠</div>
                        <div class="feature-title">Smart Caching</div>
                        <div class="feature-desc">Intelligent prompt caching</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">💾</div>
                        <div class="feature-title">Memory Optimization</div>
                        <div class="feature-desc">Automatic GPU cleanup</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>📦 Available Models</h2>
                <div id="local-models-list">
                    <div style="text-align: center; color: #6c757d; padding: 20px;">
                        🔍 Scanning for available models...
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Interface -->
        <div class="section chat-section">
            <h2>💬 Chat with Unsloth Models</h2>

            <div class="chat-controls">
                <div>
                    <label for="chat-model-select"><strong>Model:</strong></label>
                    <select id="chat-model-select" class="model-selector">
                        <option value="">Select a loaded model...</option>
                    </select>
                </div>

                <div class="chat-settings">
                    <div class="setting-group">
                        <label>Temp:</label>
                        <input type="number" id="temperature" class="setting-input" value="0.7" min="0" max="2" step="0.1">
                    </div>
                    <div class="setting-group">
                        <label>Top-p:</label>
                        <input type="number" id="top-p" class="setting-input" value="0.9" min="0" max="1" step="0.1">
                    </div>
                    <div class="setting-group">
                        <label>Max tokens:</label>
                        <input type="number" id="max-tokens" class="setting-input" value="512" min="1" max="4096" step="1" style="width: 80px;">
                    </div>
                </div>

                <button onclick="clearChat()" style="background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;">
                    🗑️ Clear Chat
                </button>
            </div>

            <div class="chat-container">
                <div class="chat-header">
                    <span>🦥</span>
                    <span>Unsloth Chat Assistant</span>
                    <span style="margin-left: auto; font-size: 0.9em;" id="chat-status">Ready</span>
                </div>

                <div class="chat-messages" id="chat-messages">
                    <div class="message system">
                        <div class="message-avatar">🤖</div>
                        <div class="message-content">
                            <strong>Welcome to Unsloth Chat!</strong><br>
                            Select a loaded model and start chatting. Unsloth provides ultra-fast inference with optimized memory usage.
                            <div class="message-meta">System • Ready</div>
                        </div>
                    </div>
                </div>

                <div class="typing-indicator" id="typing-indicator">
                    <div class="message-avatar">🦥</div>
                    <span>Assistant is thinking</span>
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>

                <div class="chat-input-container">
                    <form class="chat-input-form" onsubmit="sendMessage(event)">
                        <textarea
                            id="chat-input"
                            class="chat-input"
                            placeholder="Type your message here... (Press Ctrl+Enter to send)"
                            rows="1"
                            onkeydown="handleChatKeydown(event)"
                        ></textarea>
                        <button type="submit" class="chat-send-btn" id="chat-send-btn">
                            🚀 Send
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global chat state
        let chatHistory = [];
        let currentModel = '';

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemStatus();
            loadModelList();
            loadChatModels();

            // Auto-resize chat input
            const chatInput = document.getElementById('chat-input');
            chatInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });

            // Refresh data every 30 seconds
            setInterval(() => {
                loadSystemStatus();
                loadModelList();
                loadChatModels();
            }, 30000);
        });

        // Load system status and health
        async function loadSystemStatus() {
            try {
                const response = await fetch('/health');
                const health = await response.json();

                const statusElement = document.getElementById('system-status');
                const indicator = health.status === 'healthy' ? 'status-healthy' : 'status-error';

                statusElement.innerHTML = `
                    <div class="status-indicator ${indicator}"></div>
                    <strong>Engine:</strong> ${health.engine} v${health.version || '2.1.0'}<br>
                    <strong>Unsloth:</strong> ${health.unsloth_available ? '✅ Available' : '❌ Not Available'}<br>
                    <strong>Models Loaded:</strong> ${health.models_loaded}<br>
                    <strong>GPU Count:</strong> ${health.gpu_count}
                `;

                // Load GPU information
                if (health.gpu_count > 0) {
                    loadGPUInfo();
                }

            } catch (error) {
                console.error('Error loading system status:', error);
                document.getElementById('system-status').innerHTML = `
                    <div class="status-indicator status-error"></div>
                    <span style="color: #dc3545;">Failed to load system status</span>
                `;
            }
        }

        // Load GPU information
        async function loadGPUInfo() {
            try {
                const response = await fetch('/gpu/info');
                const gpuInfo = await response.json();

                const container = document.getElementById('gpu-info-container');

                if (gpuInfo.gpus && gpuInfo.gpus.length > 0) {
                    let gpuHTML = '<div class="gpu-info"><h3>🎮 GPU Status</h3>';

                    gpuInfo.gpus.forEach(gpu => {
                        const memoryPercent = gpu.memory_percent || 0;
                        const memoryGB = (gpu.memory_used / (1024**3)).toFixed(1);
                        const totalGB = (gpu.memory_total / (1024**3)).toFixed(1);

                        gpuHTML += `
                            <div class="gpu-card">
                                <strong>${gpu.name}</strong> (GPU ${gpu.gpu_id})<br>
                                <small>Memory: ${memoryGB}GB / ${totalGB}GB (${memoryPercent.toFixed(1)}%)</small>
                                <div class="memory-bar">
                                    <div class="memory-fill" style="width: ${memoryPercent}%"></div>
                                </div>
                                <small>Utilization: ${gpu.gpu_utilization}% • Temp: ${gpu.temperature}°C</small>
                            </div>
                        `;
                    });

                    gpuHTML += '</div>';
                    container.innerHTML = gpuHTML;
                } else {
                    container.innerHTML = '';
                }

            } catch (error) {
                console.error('Error loading GPU info:', error);
            }
        }

        // Load available models
        async function loadModelList() {
            try {
                const response = await fetch('/models/available');
                const data = await response.json();

                const container = document.getElementById('local-models-list');

                if (data.available_models && data.available_models.length > 0) {
                    container.innerHTML = data.available_models.map(model => createModelCard(model)).join('');
                } else {
                    container.innerHTML = `
                        <div style="text-align: center; color: #6c757d; padding: 20px;">
                            📭 No models found in ./models/ directory
                        </div>
                    `;
                }

            } catch (error) {
                console.error('Error loading models:', error);
                document.getElementById('local-models-list').innerHTML = `
                    <div style="text-align: center; color: #dc3545; padding: 20px;">
                        ❌ Failed to load models
                    </div>
                `;
            }
        }

        // Create model card HTML
        function createModelCard(model) {
            const escapedName = escapeHtml(model.name);
            const escapedPath = escapeHtml(model.path);

            return `
                <div class="model-card ${model.model_files === 0 ? 'model-invalid' : ''}">
                    <div class="model-info">
                        <strong>📦 ${escapedName}</strong><br>
                        <strong>Type:</strong> ${model.model_type}<br>
                        <strong>Size:</strong> ${model.size_human}<br>
                        <strong>Files:</strong> ${model.file_count} (${model.model_files} model files)<br>
                        <strong>Parameters:</strong> ${model.parameters}<br>
                        <strong>Path:</strong> ${escapedPath}
                    </div>

                    <div class="local-model-actions">
                        ${model.model_files > 0 ? `
                            <button class="load-model-btn" onclick="loadModelWithProgress('${escapedName}', '${escapedPath}')" id="load-btn-${escapedName}">
                                🚀 Load with Unsloth
                            </button>
                            <div class="progress-container" id="progress-${escapedName}">
                                <div class="progress-bar">
                                    <div class="progress-fill" id="progress-fill-${escapedName}">
                                        <div class="progress-text" id="progress-text-${escapedName}">0%</div>
                                    </div>
                                </div>
                                <div class="progress-message" id="progress-message-${escapedName}">Ready to load</div>
                                <div class="progress-stage" id="progress-stage-${escapedName}">Waiting</div>
                            </div>
                        ` : `
                            <button disabled style="background: #6c757d !important; cursor: not-allowed;">
                                ❌ Cannot Load (No Model Files)
                            </button>
                        `}
                    </div>
                </div>
            `;
        }

        // Escape HTML to prevent XSS
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Load model with progress tracking
        async function loadModelWithProgress(modelName, modelPath) {
            const button = document.getElementById(`load-btn-${modelName}`);
            const progressContainer = document.getElementById(`progress-${modelName}`);
            const progressFill = document.getElementById(`progress-fill-${modelName}`);
            const progressText = document.getElementById(`progress-text-${modelName}`);
            const progressMessage = document.getElementById(`progress-message-${modelName}`);
            const progressStage = document.getElementById(`progress-stage-${modelName}`);

            const originalText = button.textContent;

            try {
                // Update button state and show progress
                button.disabled = true;
                button.textContent = '⏳ Loading...';
                button.style.background = 'linear-gradient(45deg, #ffc107, #e0a800)';
                progressContainer.style.display = 'block';

                // Prepare load request for Unsloth
                const loadRequest = {
                    model_name: modelName,
                    model_path: modelPath,
                    max_seq_length: 2048,
                    load_in_4bit: true,
                    trust_remote_code: false
                };

                console.log('Loading model with Unsloth progress:', loadRequest);

                // Start loading
                const loadPromise = fetch('/models/load', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(loadRequest)
                });

                // Start progress monitoring
                const progressInterval = setInterval(async () => {
                    try {
                        const progressResponse = await fetch(`/models/loading-progress/${modelName}`);
                        if (progressResponse.ok) {
                            const progress = await progressResponse.json();
                            updateProgressDisplay(progress, progressFill, progressText, progressMessage, progressStage);
                        }
                    } catch (error) {
                        console.log('Progress monitoring (expected during completion):', error);
                    }
                }, 300); // Update every 300ms for smooth animation

                // Wait for loading to complete
                const response = await loadPromise;
                const result = await response.json();

                // Stop progress monitoring
                clearInterval(progressInterval);

                if (response.ok) {
                    // Show completion
                    updateProgressDisplay({
                        progress: 100,
                        stage: 'complete',
                        message: 'Model loaded successfully with Unsloth optimizations!',
                        elapsed_time: 0
                    }, progressFill, progressText, progressMessage, progressStage);

                    button.textContent = '✅ Loaded!';
                    button.style.background = 'linear-gradient(45deg, #28a745, #20c997)';

                    showNotification(`🦥 Model ${modelName} loaded successfully with Unsloth optimizations!\n💾 Memory usage: ${result.memory_usage_mb?.toFixed(1) || 'N/A'} MB`, 'success');

                    // Refresh model list after a delay
                    setTimeout(() => {
                        loadModelList();
                        progressContainer.style.display = 'none';
                    }, 4000);

                } else {
                    throw new Error(result.detail || 'Failed to load model');
                }

            } catch (error) {
                console.error('Error loading model:', error);

                // Show error state
                progressFill.style.background = 'linear-gradient(90deg, #dc3545, #c82333)';
                progressFill.style.animation = 'none';
                progressText.textContent = 'Error';
                progressMessage.textContent = error.message;
                progressStage.textContent = '❌ Failed';

                button.textContent = '❌ Failed';
                button.style.background = 'linear-gradient(45deg, #dc3545, #c82333)';

                showNotification(`❌ Failed to load model ${modelName}: ${error.message}`, 'error');

            } finally {
                // Reset button after delay
                setTimeout(() => {
                    button.disabled = false;
                    button.textContent = originalText;
                    button.style.background = 'linear-gradient(45deg, #28a745, #20c997)';
                    progressContainer.style.display = 'none';

                    // Reset progress display
                    progressFill.style.width = '0%';
                    progressFill.style.background = 'linear-gradient(90deg, #28a745, #20c997, #17a2b8)';
                    progressFill.style.animation = 'shimmer 2s infinite';
                    progressText.textContent = '0%';
                    progressMessage.textContent = 'Ready to load';
                    progressStage.textContent = '⏳ Waiting';
                }, 6000);
            }
        }

        // Update progress display with enhanced animations
        function updateProgressDisplay(progress, progressFill, progressText, progressMessage, progressStage) {
            const percentage = Math.min(100, Math.max(0, progress.progress || 0));

            progressFill.style.width = `${percentage}%`;
            progressText.textContent = `${percentage}%`;
            progressMessage.textContent = progress.message || 'Loading...';

            // Update stage with emoji and enhanced styling
            const stageEmojis = {
                'initializing': '🔄 Initializing',
                'validating': '✅ Validating',
                'preparing': '⚙️ Preparing',
                'gpu_setup': '🎮 GPU Setup',
                'loading': '📦 Loading Model',
                'optimizing': '⚡ Optimizing',
                'finalizing': '🔧 Finalizing',
                'completing': '📝 Completing',
                'complete': '🎉 Complete'
            };

            progressStage.textContent = stageEmojis[progress.stage] || progress.stage || 'Loading...';

            // Add elapsed time if available
            if (progress.elapsed_time) {
                progressStage.textContent += ` (${Math.round(progress.elapsed_time)}s)`;
            }

            // Change progress bar color based on stage
            if (progress.stage === 'complete') {
                progressFill.style.background = 'linear-gradient(90deg, #28a745, #20c997)';
                progressFill.style.animation = 'none';
            } else if (percentage > 0) {
                progressFill.style.background = 'linear-gradient(90deg, #28a745, #20c997, #17a2b8)';
                progressFill.style.animation = 'shimmer 2s infinite';
            }
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }

        // Load available models for chat
        async function loadChatModels() {
            try {
                const response = await fetch('/models/info');
                const models = await response.json();

                const select = document.getElementById('chat-model-select');
                const currentValue = select.value;

                // Clear existing options except the first one
                select.innerHTML = '<option value="">Select a loaded model...</option>';

                models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.name;
                    option.textContent = `${model.name} (${model.status})`;
                    if (model.status !== 'loaded') {
                        option.disabled = true;
                        option.textContent += ' - Not Ready';
                    }
                    select.appendChild(option);
                });

                // Restore previous selection if still available
                if (currentValue && [...select.options].some(opt => opt.value === currentValue)) {
                    select.value = currentValue;
                    currentModel = currentValue;
                }

                // Update chat status
                updateChatStatus();

            } catch (error) {
                console.error('Error loading chat models:', error);
            }
        }

        // Update chat status
        function updateChatStatus() {
            const statusElement = document.getElementById('chat-status');
            const modelSelect = document.getElementById('chat-model-select');

            if (modelSelect.value) {
                statusElement.textContent = `Ready with ${modelSelect.value}`;
                statusElement.style.color = '#20c997';
            } else {
                statusElement.textContent = 'Select a model to start chatting';
                statusElement.style.color = '#ffc107';
            }
        }

        // Handle model selection change
        document.addEventListener('DOMContentLoaded', function() {
            const modelSelect = document.getElementById('chat-model-select');
            modelSelect.addEventListener('change', function() {
                currentModel = this.value;
                updateChatStatus();

                if (currentModel) {
                    addSystemMessage(`Switched to model: ${currentModel}`);
                }
            });
        });

        // Handle chat input keydown
        function handleChatKeydown(event) {
            if (event.ctrlKey && event.key === 'Enter') {
                event.preventDefault();
                sendMessage(event);
            }
        }

        // Send chat message
        async function sendMessage(event) {
            event.preventDefault();

            const input = document.getElementById('chat-input');
            const sendBtn = document.getElementById('chat-send-btn');
            const message = input.value.trim();

            if (!message) return;

            if (!currentModel) {
                showNotification('Please select a model first', 'error');
                return;
            }

            // Add user message to chat
            addMessage('user', message);

            // Clear input and disable send button
            input.value = '';
            input.style.height = 'auto';
            sendBtn.disabled = true;
            sendBtn.textContent = '⏳ Sending...';

            // Show typing indicator
            showTypingIndicator();

            try {
                // Prepare chat request
                const chatRequest = {
                    model_name: currentModel,
                    messages: chatHistory,
                    max_new_tokens: parseInt(document.getElementById('max-tokens').value) || 512,
                    temperature: parseFloat(document.getElementById('temperature').value) || 0.7,
                    top_p: parseFloat(document.getElementById('top-p').value) || 0.9,
                    do_sample: true
                };

                console.log('Sending chat request:', chatRequest);

                const response = await fetch('/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(chatRequest)
                });

                const result = await response.json();

                if (response.ok) {
                    // Add assistant response
                    addMessage('assistant', result.message.content, {
                        tokens: result.tokens_generated,
                        latency: result.latency,
                        total_tokens: result.total_tokens
                    });

                    showNotification(`Response generated in ${result.latency.toFixed(2)}s`, 'success');
                } else {
                    throw new Error(result.detail || 'Failed to get response');
                }

            } catch (error) {
                console.error('Chat error:', error);
                addMessage('system', `Error: ${error.message}`, null, 'error');
                showNotification(`Chat error: ${error.message}`, 'error');
            } finally {
                // Re-enable send button and hide typing indicator
                sendBtn.disabled = false;
                sendBtn.textContent = '🚀 Send';
                hideTypingIndicator();
            }
        }

        // Add message to chat
        function addMessage(role, content, meta = null, type = 'normal') {
            const messagesContainer = document.getElementById('chat-messages');

            // Add to chat history (for API)
            if (role !== 'system' || type !== 'error') {
                chatHistory.push({ role, content });
            }

            // Create message element
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = role === 'user' ? '👤' : role === 'assistant' ? '🦥' : '🤖';

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            if (type === 'error') {
                messageContent.style.background = 'linear-gradient(135deg, #dc3545, #c82333)';
                messageContent.style.color = 'white';
            }

            let contentHTML = `<div>${escapeHtml(content)}</div>`;

            // Add metadata
            let metaText = new Date().toLocaleTimeString();
            if (meta) {
                metaText += ` • ${meta.tokens} tokens • ${meta.latency.toFixed(2)}s`;
                if (meta.total_tokens) {
                    metaText += ` • Total: ${meta.total_tokens}`;
                }
            }

            contentHTML += `<div class="message-meta">${metaText}</div>`;
            messageContent.innerHTML = contentHTML;

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);

            messagesContainer.appendChild(messageDiv);

            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Add system message
        function addSystemMessage(content) {
            addMessage('system', content);
        }

        // Show typing indicator
        function showTypingIndicator() {
            document.getElementById('typing-indicator').style.display = 'flex';
            const messagesContainer = document.getElementById('chat-messages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Hide typing indicator
        function hideTypingIndicator() {
            document.getElementById('typing-indicator').style.display = 'none';
        }

        // Clear chat
        function clearChat() {
            if (confirm('Are you sure you want to clear the chat history?')) {
                chatHistory = [];
                const messagesContainer = document.getElementById('chat-messages');
                messagesContainer.innerHTML = `
                    <div class="message system">
                        <div class="message-avatar">🤖</div>
                        <div class="message-content">
                            <strong>Chat cleared!</strong><br>
                            Ready for a new conversation with Unsloth.
                            <div class="message-meta">System • ${new Date().toLocaleTimeString()}</div>
                        </div>
                    </div>
                `;
                showNotification('Chat history cleared', 'success');
            }
        }
    </script>
</body>
</html>
